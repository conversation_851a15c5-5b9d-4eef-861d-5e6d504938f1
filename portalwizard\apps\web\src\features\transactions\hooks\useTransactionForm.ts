/**
 * useTransactionForm Hook
 *
 * Custom hook for managing transaction form state and submission.
 */

import { useForm } from '@tanstack/react-form';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';
import { TRPCClientError } from '@trpc/client';
import { trpc } from '@/utils/trpc';
import type { TransactionFormValues } from '../types';

export function useTransactionForm() {
  // Set up the mutation for creating a transaction
  // Set up the mutation for creating a transaction
  const createTransaction = useMutation({
    ...trpc.transactions.create.mutationOptions(),
    onSuccess: () => {
      toast.success('Transaction created successfully!');
    },
    onError: (error) => {
      // Handle both TRPCClientError and regular Error types
      const errorMessage = error instanceof TRPCClientError
        ? error.message
        : error instanceof Error
          ? error.message
          : 'Unknown error';
      toast.error(`Error creating transaction: ${errorMessage}`);
    }
  });

  // Initialize the form with default values
  // Initialize the form with default values
  const form = useForm({
    defaultValues: {
      // Step 0: Transaction Type & Date
      marketType: 'secondary',
      transactionType: '',
      transactionDate: '',

      // Step 1-2: Property Details
      propertyName: '',
      propertyType: '',
      address: '',
      totalPrice: '',
      monthlyRent: '', // For lease transactions
      propertyDeveloper: '',
      propertyProject: '',
      propertyUnitNumber: '', // New field for property unit number
      selectedProperty: false, // Tracking property selection for Primary Market

      // Additional property details
      builtUpArea: '',
      landArea: '',
      bedrooms: '',
      bathrooms: '',
      carParks: '',
      furnishing: '',
      propertyFeatures: '',

      // Step 3: Client Information
      clientName: '',
      clientEmail: '',
      clientPhone: '',
      clientIdNumber: '',
      clientAcquisitionSource: '',

      // Step 4: Co-Broking Setup
      coBrokingEnabled: false,
      coBrokingDirection: 'buyer' as 'seller' | 'buyer',
      coBrokingAgentName: '',
      coBrokingAgentRen: '',
      coBrokingAgencyName: '',
      coBrokingAgentContact: '',

      // Step 5: Commission Calculation
      commissionValue: '',
      commissionType: '',
      commissionPercentage: '',

      // Step 6: Documents
      documents: [] as File[],

      // Step 7: Review
      notes: '',
      isAgencyListing: false,
    } as TransactionFormValues,

    // Form submission handler
    onSubmit: async ({ value }) => {
      try {
        // Transform form data to match tRPC schema defined in createTransactionSchema
        const transactionData = {
          // Step 1: Transaction Type & Date
          marketType: value.marketType as "primary" | "secondary",
          transactionType: value.transactionType as "sale" | "lease",
          transactionDate: new Date(value.transactionDate).toISOString(),

          // Step 2: Property Details
          propertyDetails: {
            name: value.propertyName,
            type: value.propertyType as "residential_villa" | "residential_apartment" | "residential_townhouse" | "commercial_office" | "commercial_retail" | "commercial_warehouse" | "land_residential" | "land_commercial" | "industrial_factory" | "other",
            address: value.address,
            developer: value.propertyDeveloper || undefined,
            project: value.propertyProject || undefined,
            unitNumber: value.propertyUnitNumber || undefined, // New field
          },

          // Step 3: Client Information - These must be at root level as per API schema
          clientName: value.clientName,
          clientEmail: value.clientEmail || "",
          clientPhone: value.clientPhone || "",
          clientType: "buyer" as "buyer" | "seller" | "tenant" | "landlord", // Default to buyer, should be set based on form
          clientIdNumber: value.clientIdNumber || "",

          // Step 4: Co-Broking Setup
          coBrokingType: value.coBrokingEnabled ? "co_broke" : "direct" as "direct" | "co_broke",
          coBrokingAgentName: value.coBrokingEnabled ? value.coBrokingAgentName : undefined,
          coBrokingAgencyName: value.coBrokingEnabled ? value.coBrokingAgencyName : undefined,
          coBrokingAgentRera: value.coBrokingEnabled ? value.coBrokingAgentRen : undefined,

          // Step 5: Commission Calculation
          totalPrice: value.totalPrice || "0",
          commissionValue: value.commissionValue || "0",
          // Handle both fixed_amount (new) and fixed (legacy) values
          commissionType: ((value.commissionType as string) === "fixed" ? "fixed_amount" : value.commissionType) as "percentage" | "fixed_amount",
          commissionPercentage: value.commissionPercentage || undefined,

          // Step 6: Notes
          notes: value.notes || undefined,
        };

        // Submit the transaction
        await createTransaction.mutateAsync(transactionData);

        return { status: 'success' };
      } catch (error: any) {
        console.error('Error submitting transaction:', error);
        return { status: 'error', message: error.message };
      }
    },
  });

  return {
    form,
    isSubmitting: createTransaction.isPending,
    isSuccess: createTransaction.isSuccess,
    error: createTransaction.error,
  };
}
