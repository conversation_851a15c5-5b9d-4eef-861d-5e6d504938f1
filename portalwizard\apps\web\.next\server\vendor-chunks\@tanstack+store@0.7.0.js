"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack+store@0.7.0";
exports.ids = ["vendor-chunks/@tanstack+store@0.7.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/derived.js":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/derived.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Derived: () => (/* binding */ Derived)\n/* harmony export */ });\n/* harmony import */ var _store_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./store.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/store.js\");\n/* harmony import */ var _scheduler_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./scheduler.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/scheduler.js\");\n\n\nclass Derived {\n  constructor(options) {\n    this.listeners = /* @__PURE__ */ new Set();\n    this._subscriptions = [];\n    this.lastSeenDepValues = [];\n    this.getDepVals = () => {\n      const prevDepVals = [];\n      const currDepVals = [];\n      for (const dep of this.options.deps) {\n        prevDepVals.push(dep.prevState);\n        currDepVals.push(dep.state);\n      }\n      this.lastSeenDepValues = currDepVals;\n      return {\n        prevDepVals,\n        currDepVals,\n        prevVal: this.prevState ?? void 0\n      };\n    };\n    this.recompute = () => {\n      var _a, _b;\n      this.prevState = this.state;\n      const { prevDepVals, currDepVals, prevVal } = this.getDepVals();\n      this.state = this.options.fn({\n        prevDepVals,\n        currDepVals,\n        prevVal\n      });\n      (_b = (_a = this.options).onUpdate) == null ? void 0 : _b.call(_a);\n    };\n    this.checkIfRecalculationNeededDeeply = () => {\n      for (const dep of this.options.deps) {\n        if (dep instanceof Derived) {\n          dep.checkIfRecalculationNeededDeeply();\n        }\n      }\n      let shouldRecompute = false;\n      const lastSeenDepValues = this.lastSeenDepValues;\n      const { currDepVals } = this.getDepVals();\n      for (let i = 0; i < currDepVals.length; i++) {\n        if (currDepVals[i] !== lastSeenDepValues[i]) {\n          shouldRecompute = true;\n          break;\n        }\n      }\n      if (shouldRecompute) {\n        this.recompute();\n      }\n    };\n    this.mount = () => {\n      this.registerOnGraph();\n      this.checkIfRecalculationNeededDeeply();\n      return () => {\n        this.unregisterFromGraph();\n        for (const cleanup of this._subscriptions) {\n          cleanup();\n        }\n      };\n    };\n    this.subscribe = (listener) => {\n      var _a, _b;\n      this.listeners.add(listener);\n      const unsub = (_b = (_a = this.options).onSubscribe) == null ? void 0 : _b.call(_a, listener, this);\n      return () => {\n        this.listeners.delete(listener);\n        unsub == null ? void 0 : unsub();\n      };\n    };\n    this.options = options;\n    this.state = options.fn({\n      prevDepVals: void 0,\n      prevVal: void 0,\n      currDepVals: this.getDepVals().currDepVals\n    });\n  }\n  registerOnGraph(deps = this.options.deps) {\n    for (const dep of deps) {\n      if (dep instanceof Derived) {\n        dep.registerOnGraph();\n        this.registerOnGraph(dep.options.deps);\n      } else if (dep instanceof _store_js__WEBPACK_IMPORTED_MODULE_0__.Store) {\n        let relatedLinkedDerivedVals = _scheduler_js__WEBPACK_IMPORTED_MODULE_1__.__storeToDerived.get(dep);\n        if (!relatedLinkedDerivedVals) {\n          relatedLinkedDerivedVals = /* @__PURE__ */ new Set();\n          _scheduler_js__WEBPACK_IMPORTED_MODULE_1__.__storeToDerived.set(dep, relatedLinkedDerivedVals);\n        }\n        relatedLinkedDerivedVals.add(this);\n        let relatedStores = _scheduler_js__WEBPACK_IMPORTED_MODULE_1__.__derivedToStore.get(this);\n        if (!relatedStores) {\n          relatedStores = /* @__PURE__ */ new Set();\n          _scheduler_js__WEBPACK_IMPORTED_MODULE_1__.__derivedToStore.set(this, relatedStores);\n        }\n        relatedStores.add(dep);\n      }\n    }\n  }\n  unregisterFromGraph(deps = this.options.deps) {\n    for (const dep of deps) {\n      if (dep instanceof Derived) {\n        this.unregisterFromGraph(dep.options.deps);\n      } else if (dep instanceof _store_js__WEBPACK_IMPORTED_MODULE_0__.Store) {\n        const relatedLinkedDerivedVals = _scheduler_js__WEBPACK_IMPORTED_MODULE_1__.__storeToDerived.get(dep);\n        if (relatedLinkedDerivedVals) {\n          relatedLinkedDerivedVals.delete(this);\n        }\n        const relatedStores = _scheduler_js__WEBPACK_IMPORTED_MODULE_1__.__derivedToStore.get(this);\n        if (relatedStores) {\n          relatedStores.delete(dep);\n        }\n      }\n    }\n  }\n}\n\n//# sourceMappingURL=derived.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/derived.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/effect.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/effect.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Effect: () => (/* binding */ Effect)\n/* harmony export */ });\n/* harmony import */ var _derived_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./derived.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/derived.js\");\n\nclass Effect {\n  constructor(opts) {\n    const { eager, fn, ...derivedProps } = opts;\n    this._derived = new _derived_js__WEBPACK_IMPORTED_MODULE_0__.Derived({\n      ...derivedProps,\n      fn: () => {\n      },\n      onUpdate() {\n        fn();\n      }\n    });\n    if (eager) {\n      fn();\n    }\n  }\n  mount() {\n    return this._derived.mount();\n  }\n}\n\n//# sourceMappingURL=effect.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0YW5zdGFjaytzdG9yZUAwLjcuMC9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3N0b3JlL2Rpc3QvZXNtL2VmZmVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QztBQUN2QztBQUNBO0FBQ0EsWUFBWSw2QkFBNkI7QUFDekMsd0JBQXdCLGdEQUFPO0FBQy9CO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVIgMVxcRGVza3RvcFxcZGV2b3RzcC12MS41XFxwb3J0YWx3aXphcmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEB0YW5zdGFjaytzdG9yZUAwLjcuMFxcbm9kZV9tb2R1bGVzXFxAdGFuc3RhY2tcXHN0b3JlXFxkaXN0XFxlc21cXGVmZmVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBEZXJpdmVkIH0gZnJvbSBcIi4vZGVyaXZlZC5qc1wiO1xuY2xhc3MgRWZmZWN0IHtcbiAgY29uc3RydWN0b3Iob3B0cykge1xuICAgIGNvbnN0IHsgZWFnZXIsIGZuLCAuLi5kZXJpdmVkUHJvcHMgfSA9IG9wdHM7XG4gICAgdGhpcy5fZGVyaXZlZCA9IG5ldyBEZXJpdmVkKHtcbiAgICAgIC4uLmRlcml2ZWRQcm9wcyxcbiAgICAgIGZuOiAoKSA9PiB7XG4gICAgICB9LFxuICAgICAgb25VcGRhdGUoKSB7XG4gICAgICAgIGZuKCk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgaWYgKGVhZ2VyKSB7XG4gICAgICBmbigpO1xuICAgIH1cbiAgfVxuICBtb3VudCgpIHtcbiAgICByZXR1cm4gdGhpcy5fZGVyaXZlZC5tb3VudCgpO1xuICB9XG59XG5leHBvcnQge1xuICBFZmZlY3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1lZmZlY3QuanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/effect.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/index.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/index.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Derived: () => (/* reexport safe */ _derived_js__WEBPACK_IMPORTED_MODULE_0__.Derived),\n/* harmony export */   Effect: () => (/* reexport safe */ _effect_js__WEBPACK_IMPORTED_MODULE_1__.Effect),\n/* harmony export */   Store: () => (/* reexport safe */ _store_js__WEBPACK_IMPORTED_MODULE_2__.Store),\n/* harmony export */   __depsThatHaveWrittenThisTick: () => (/* reexport safe */ _scheduler_js__WEBPACK_IMPORTED_MODULE_3__.__depsThatHaveWrittenThisTick),\n/* harmony export */   __derivedToStore: () => (/* reexport safe */ _scheduler_js__WEBPACK_IMPORTED_MODULE_3__.__derivedToStore),\n/* harmony export */   __flush: () => (/* reexport safe */ _scheduler_js__WEBPACK_IMPORTED_MODULE_3__.__flush),\n/* harmony export */   __storeToDerived: () => (/* reexport safe */ _scheduler_js__WEBPACK_IMPORTED_MODULE_3__.__storeToDerived),\n/* harmony export */   batch: () => (/* reexport safe */ _scheduler_js__WEBPACK_IMPORTED_MODULE_3__.batch)\n/* harmony export */ });\n/* harmony import */ var _derived_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./derived.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/derived.js\");\n/* harmony import */ var _effect_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./effect.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/effect.js\");\n/* harmony import */ var _store_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./store.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/store.js\");\n/* harmony import */ var _scheduler_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./scheduler.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/scheduler.js\");\n\n\n\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0YW5zdGFjaytzdG9yZUAwLjcuMC9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3N0b3JlL2Rpc3QvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUF1QztBQUNGO0FBQ0Y7QUFDZ0Y7QUFVakg7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSIDFcXERlc2t0b3BcXGRldm90c3AtdjEuNVxccG9ydGFsd2l6YXJkXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAdGFuc3RhY2src3RvcmVAMC43LjBcXG5vZGVfbW9kdWxlc1xcQHRhbnN0YWNrXFxzdG9yZVxcZGlzdFxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBEZXJpdmVkIH0gZnJvbSBcIi4vZGVyaXZlZC5qc1wiO1xuaW1wb3J0IHsgRWZmZWN0IH0gZnJvbSBcIi4vZWZmZWN0LmpzXCI7XG5pbXBvcnQgeyBTdG9yZSB9IGZyb20gXCIuL3N0b3JlLmpzXCI7XG5pbXBvcnQgeyBfX2RlcHNUaGF0SGF2ZVdyaXR0ZW5UaGlzVGljaywgX19kZXJpdmVkVG9TdG9yZSwgX19mbHVzaCwgX19zdG9yZVRvRGVyaXZlZCwgYmF0Y2ggfSBmcm9tIFwiLi9zY2hlZHVsZXIuanNcIjtcbmV4cG9ydCB7XG4gIERlcml2ZWQsXG4gIEVmZmVjdCxcbiAgU3RvcmUsXG4gIF9fZGVwc1RoYXRIYXZlV3JpdHRlblRoaXNUaWNrLFxuICBfX2Rlcml2ZWRUb1N0b3JlLFxuICBfX2ZsdXNoLFxuICBfX3N0b3JlVG9EZXJpdmVkLFxuICBiYXRjaFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/scheduler.js":
/*!*********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/scheduler.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __depsThatHaveWrittenThisTick: () => (/* binding */ __depsThatHaveWrittenThisTick),\n/* harmony export */   __derivedToStore: () => (/* binding */ __derivedToStore),\n/* harmony export */   __flush: () => (/* binding */ __flush),\n/* harmony export */   __storeToDerived: () => (/* binding */ __storeToDerived),\n/* harmony export */   batch: () => (/* binding */ batch)\n/* harmony export */ });\n/* harmony import */ var _derived_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./derived.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/derived.js\");\n\nconst __storeToDerived = /* @__PURE__ */ new WeakMap();\nconst __derivedToStore = /* @__PURE__ */ new WeakMap();\nconst __depsThatHaveWrittenThisTick = {\n  current: []\n};\nlet __isFlushing = false;\nlet __batchDepth = 0;\nconst __pendingUpdates = /* @__PURE__ */ new Set();\nconst __initialBatchValues = /* @__PURE__ */ new Map();\nfunction __flush_internals(relatedVals) {\n  const sorted = Array.from(relatedVals).sort((a, b) => {\n    if (a instanceof _derived_js__WEBPACK_IMPORTED_MODULE_0__.Derived && a.options.deps.includes(b)) return 1;\n    if (b instanceof _derived_js__WEBPACK_IMPORTED_MODULE_0__.Derived && b.options.deps.includes(a)) return -1;\n    return 0;\n  });\n  for (const derived of sorted) {\n    if (__depsThatHaveWrittenThisTick.current.includes(derived)) {\n      continue;\n    }\n    __depsThatHaveWrittenThisTick.current.push(derived);\n    derived.recompute();\n    const stores = __derivedToStore.get(derived);\n    if (stores) {\n      for (const store of stores) {\n        const relatedLinkedDerivedVals = __storeToDerived.get(store);\n        if (!relatedLinkedDerivedVals) continue;\n        __flush_internals(relatedLinkedDerivedVals);\n      }\n    }\n  }\n}\nfunction __notifyListeners(store) {\n  store.listeners.forEach(\n    (listener) => listener({\n      prevVal: store.prevState,\n      currentVal: store.state\n    })\n  );\n}\nfunction __notifyDerivedListeners(derived) {\n  derived.listeners.forEach(\n    (listener) => listener({\n      prevVal: derived.prevState,\n      currentVal: derived.state\n    })\n  );\n}\nfunction __flush(store) {\n  if (__batchDepth > 0 && !__initialBatchValues.has(store)) {\n    __initialBatchValues.set(store, store.prevState);\n  }\n  __pendingUpdates.add(store);\n  if (__batchDepth > 0) return;\n  if (__isFlushing) return;\n  try {\n    __isFlushing = true;\n    while (__pendingUpdates.size > 0) {\n      const stores = Array.from(__pendingUpdates);\n      __pendingUpdates.clear();\n      for (const store2 of stores) {\n        const prevState = __initialBatchValues.get(store2) ?? store2.prevState;\n        store2.prevState = prevState;\n        __notifyListeners(store2);\n      }\n      for (const store2 of stores) {\n        const derivedVals = __storeToDerived.get(store2);\n        if (!derivedVals) continue;\n        __depsThatHaveWrittenThisTick.current.push(store2);\n        __flush_internals(derivedVals);\n      }\n      for (const store2 of stores) {\n        const derivedVals = __storeToDerived.get(store2);\n        if (!derivedVals) continue;\n        for (const derived of derivedVals) {\n          __notifyDerivedListeners(derived);\n        }\n      }\n    }\n  } finally {\n    __isFlushing = false;\n    __depsThatHaveWrittenThisTick.current = [];\n    __initialBatchValues.clear();\n  }\n}\nfunction batch(fn) {\n  __batchDepth++;\n  try {\n    fn();\n  } finally {\n    __batchDepth--;\n    if (__batchDepth === 0) {\n      const pendingUpdateToFlush = Array.from(__pendingUpdates)[0];\n      if (pendingUpdateToFlush) {\n        __flush(pendingUpdateToFlush);\n      }\n    }\n  }\n}\n\n//# sourceMappingURL=scheduler.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/scheduler.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/store.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/store.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Store: () => (/* binding */ Store)\n/* harmony export */ });\n/* harmony import */ var _scheduler_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./scheduler.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/scheduler.js\");\n\nclass Store {\n  constructor(initialState, options) {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.subscribe = (listener) => {\n      var _a, _b;\n      this.listeners.add(listener);\n      const unsub = (_b = (_a = this.options) == null ? void 0 : _a.onSubscribe) == null ? void 0 : _b.call(_a, listener, this);\n      return () => {\n        this.listeners.delete(listener);\n        unsub == null ? void 0 : unsub();\n      };\n    };\n    this.setState = (updater) => {\n      var _a, _b, _c;\n      this.prevState = this.state;\n      this.state = ((_a = this.options) == null ? void 0 : _a.updateFn) ? this.options.updateFn(this.prevState)(updater) : updater(this.prevState);\n      (_c = (_b = this.options) == null ? void 0 : _b.onUpdate) == null ? void 0 : _c.call(_b);\n      (0,_scheduler_js__WEBPACK_IMPORTED_MODULE_0__.__flush)(this);\n    };\n    this.prevState = initialState;\n    this.state = initialState;\n    this.options = options;\n  }\n}\n\n//# sourceMappingURL=store.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/store.js\n");

/***/ })

};
;