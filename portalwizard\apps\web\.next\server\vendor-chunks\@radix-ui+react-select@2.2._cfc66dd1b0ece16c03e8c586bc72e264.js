"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-select@2.2._cfc66dd1b0ece16c03e8c586bc72e264";
exports.ids = ["vendor-chunks/@radix-ui+react-select@2.2._cfc66dd1b0ece16c03e8c586bc72e264"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-select@2.2._cfc66dd1b0ece16c03e8c586bc72e264/node_modules/@radix-ui/react-select/dist/index.mjs":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-select@2.2._cfc66dd1b0ece16c03e8c586bc72e264/node_modules/@radix-ui/react-select/dist/index.mjs ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   ItemText: () => (/* binding */ ItemText),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   ScrollDownButton: () => (/* binding */ ScrollDownButton),\n/* harmony export */   ScrollUpButton: () => (/* binding */ ScrollUpButton),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectArrow: () => (/* binding */ SelectArrow),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectIcon: () => (/* binding */ SelectIcon),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectItemIndicator: () => (/* binding */ SelectItemIndicator),\n/* harmony export */   SelectItemText: () => (/* binding */ SelectItemText),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectPortal: () => (/* binding */ SelectPortal),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue),\n/* harmony export */   SelectViewport: () => (/* binding */ SelectViewport),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Value: () => (/* binding */ Value),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createSelectScope: () => (/* binding */ createSelectScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+number@1.1.1/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-collection@_bea6b1187739388da1b9eacd35107e12/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-compose-ref_2238c4c36a8d42905b4a0c9b20269af1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1_a8f58efc49c849094cf13e0699195f5f/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-direction@1_7f3e34af5adb4a5fc9d102b622811d48/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-dismissable_38da9515467d403aedc2c6a34ceb0a57/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-focus-guard_23abb663d9de5b5507bcb8dc1ae6eeee/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-focus-scope_89f81dfc69ec5ce6da535a0118061a60/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.3_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-popper@1.2._b55878cb566627402ffbd41eda4c7e0d/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-portal@1.1._48ad08c7ae2c677a34c4eecf6078a159/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-primitive@2_e1a155cffd5059c05e9aa59be25b95f1/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-slot@1.2.2_@types+react@18.3.3_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-callbac_47c065e45719ebf277832e97251a8e29/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-control_4c7ee7132b3ac1398392a7a75202c18a/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-layout-_66f6001c9fe78a4b49c68b1f72e6db1a/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-previou_b60147bd1630430bc305b89235c29831/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-visually-hi_a9f47e548ff1a4ce19eb866efbed8fe8/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/../../node_modules/.pnpm/aria-hidden@1.2.4/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.6.3_@types+react@18.3.3_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Group,Icon,Item,ItemIndicator,ItemText,Label,Portal,Root,ScrollDownButton,ScrollUpButton,Select,SelectArrow,SelectContent,SelectGroup,SelectIcon,SelectItem,SelectItemIndicator,SelectItemText,SelectLabel,SelectPortal,SelectScrollDownButton,SelectScrollUpButton,SelectSeparator,SelectTrigger,SelectValue,SelectViewport,Separator,Trigger,Value,Viewport,createSelectScope auto */ // src/select.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar OPEN_KEYS = [\n    \" \",\n    \"Enter\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar SELECTION_KEYS = [\n    \" \",\n    \"Enter\"\n];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(SELECT_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope)();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props)=>{\n    const { __scopeSelect, children, open: openProp, defaultOpen, onOpenChange, value: valueProp, defaultValue, onValueChange, dir, name, autoComplete, disabled, required, form } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNode, setValueNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNodeHasChildren, setValueNodeHasChildren] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: SELECT_NAME\n    });\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange,\n        caller: SELECT_NAME\n    });\n    const triggerPointerDownPosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n    const [nativeOptionsSet, setNativeOptionsSet] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Set());\n    const nativeSelectKey = Array.from(nativeOptionsSet).map((option)=>option.props.value).join(\";\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectProvider, {\n            required,\n            scope: __scopeSelect,\n            trigger,\n            onTriggerChange: setTrigger,\n            valueNode,\n            onValueNodeChange: setValueNode,\n            valueNodeHasChildren,\n            onValueNodeHasChildrenChange: setValueNodeHasChildren,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)(),\n            value,\n            onValueChange: setValue,\n            open,\n            onOpenChange: setOpen,\n            dir: direction,\n            triggerPointerDownPosRef,\n            disabled,\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n                    scope: __scopeSelect,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectNativeOptionsProvider, {\n                        scope: props.__scopeSelect,\n                        onNativeOptionAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                            \"Select.useCallback\": (option)=>{\n                                setNativeOptionsSet({\n                                    \"Select.useCallback\": (prev)=>new Set(prev).add(option)\n                                }[\"Select.useCallback\"]);\n                            }\n                        }[\"Select.useCallback\"], []),\n                        onNativeOptionRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                            \"Select.useCallback\": (option)=>{\n                                setNativeOptionsSet({\n                                    \"Select.useCallback\": (prev)=>{\n                                        const optionsSet = new Set(prev);\n                                        optionsSet.delete(option);\n                                        return optionsSet;\n                                    }\n                                }[\"Select.useCallback\"]);\n                            }\n                        }[\"Select.useCallback\"], []),\n                        children\n                    })\n                }),\n                isFormControl ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectBubbleInput, {\n                    \"aria-hidden\": true,\n                    required,\n                    tabIndex: -1,\n                    name,\n                    autoComplete,\n                    value,\n                    onChange: (event)=>setValue(event.target.value),\n                    disabled,\n                    form,\n                    children: [\n                        value === void 0 ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                            value: \"\"\n                        }) : null,\n                        Array.from(nativeOptionsSet)\n                    ]\n                }, nativeSelectKey) : null\n            ]\n        })\n    });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch({\n        \"SelectTrigger.useTypeaheadSearch\": (search)=>{\n            const enabledItems = getItems().filter({\n                \"SelectTrigger.useTypeaheadSearch.enabledItems\": (item)=>!item.disabled\n            }[\"SelectTrigger.useTypeaheadSearch.enabledItems\"]);\n            const currentItem = enabledItems.find({\n                \"SelectTrigger.useTypeaheadSearch.currentItem\": (item)=>item.value === context.value\n            }[\"SelectTrigger.useTypeaheadSearch.currentItem\"]);\n            const nextItem = findNextItem(enabledItems, search, currentItem);\n            if (nextItem !== void 0) {\n                context.onValueChange(nextItem.value);\n            }\n        }\n    }[\"SelectTrigger.useTypeaheadSearch\"]);\n    const handleOpen = (pointerEvent)=>{\n        if (!isDisabled) {\n            context.onOpenChange(true);\n            resetTypeahead();\n        }\n        if (pointerEvent) {\n            context.triggerPointerDownPosRef.current = {\n                x: Math.round(pointerEvent.pageX),\n                y: Math.round(pointerEvent.pageY)\n            };\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.button, {\n            type: \"button\",\n            role: \"combobox\",\n            \"aria-controls\": context.contentId,\n            \"aria-expanded\": context.open,\n            \"aria-required\": context.required,\n            \"aria-autocomplete\": \"none\",\n            dir: context.dir,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            disabled: isDisabled,\n            \"data-disabled\": isDisabled ? \"\" : void 0,\n            \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n            ...triggerProps,\n            ref: composedRefs,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onClick, (event)=>{\n                event.currentTarget.focus();\n                if (pointerTypeRef.current !== \"mouse\") {\n                    handleOpen(event);\n                }\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onPointerDown, (event)=>{\n                pointerTypeRef.current = event.pointerType;\n                const target = event.target;\n                if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                }\n                if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n                    handleOpen(event);\n                    event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onKeyDown, (event)=>{\n                const isTypingAhead = searchRef.current !== \"\";\n                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                if (isTypingAhead && event.key === \" \") return;\n                if (OPEN_KEYS.includes(event.key)) {\n                    handleOpen();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onValueNodeChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectValue.useLayoutEffect\": ()=>{\n            onValueNodeHasChildrenChange(hasChildren);\n        }\n    }[\"SelectValue.useLayoutEffect\"], [\n        onValueNodeHasChildrenChange,\n        hasChildren\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        ...valueProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: \"none\"\n        },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n            children: placeholder\n        }) : children\n    });\n});\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...iconProps,\n        ref: forwardedRef,\n        children: children || \"\\u25BC\"\n    });\n});\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        ...props\n    });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectContent.useLayoutEffect\": ()=>{\n            setFragment(new DocumentFragment());\n        }\n    }[\"SelectContent.useLayoutEffect\"], []);\n    if (!context.open) {\n        const frag = fragment;\n        return frag ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n            scope: props.__scopeSelect,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: props.__scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n                    children: props.children\n                })\n            })\n        }), frag) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentImpl, {\n        ...props,\n        ref: forwardedRef\n    });\n});\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__.createSlot)(\"SelectContent.RemoveScroll\");\nvar SelectContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, position = \"item-aligned\", onCloseAutoFocus, onEscapeKeyDown, onPointerDownOutside, //\n    // PopperContent props\n    side, sideOffset, align, alignOffset, arrowPadding, collisionBoundary, collisionPadding, sticky, hideWhenDetached, avoidCollisions, //\n    ...contentProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectContentImpl.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"SelectContentImpl.useComposedRefs[composedRefs]\"]);\n    const [selectedItem, setSelectedItem] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [selectedItemText, setSelectedItemText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const firstValidItemFoundRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_15__.hideOthers)(content);\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        content\n    ]);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_16__.useFocusGuards)();\n    const focusFirst = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[focusFirst]\": (candidates)=>{\n            const [firstItem, ...restItems] = getItems().map({\n                \"SelectContentImpl.useCallback[focusFirst]\": (item)=>item.ref.current\n            }[\"SelectContentImpl.useCallback[focusFirst]\"]);\n            const [lastItem] = restItems.slice(-1);\n            const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n            for (const candidate of candidates){\n                if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n                candidate?.scrollIntoView({\n                    block: \"nearest\"\n                });\n                if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n                if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n                candidate?.focus();\n                if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n            }\n        }\n    }[\"SelectContentImpl.useCallback[focusFirst]\"], [\n        getItems,\n        viewport\n    ]);\n    const focusSelectedItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[focusSelectedItem]\": ()=>focusFirst([\n                selectedItem,\n                content\n            ])\n    }[\"SelectContentImpl.useCallback[focusSelectedItem]\"], [\n        focusFirst,\n        selectedItem,\n        content\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            if (isPositioned) {\n                focusSelectedItem();\n            }\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        isPositioned,\n        focusSelectedItem\n    ]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            if (content) {\n                let pointerMoveDelta = {\n                    x: 0,\n                    y: 0\n                };\n                const handlePointerMove = {\n                    \"SelectContentImpl.useEffect.handlePointerMove\": (event)=>{\n                        pointerMoveDelta = {\n                            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n                            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n                        };\n                    }\n                }[\"SelectContentImpl.useEffect.handlePointerMove\"];\n                const handlePointerUp = {\n                    \"SelectContentImpl.useEffect.handlePointerUp\": (event)=>{\n                        if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n                            event.preventDefault();\n                        } else {\n                            if (!content.contains(event.target)) {\n                                onOpenChange(false);\n                            }\n                        }\n                        document.removeEventListener(\"pointermove\", handlePointerMove);\n                        triggerPointerDownPosRef.current = null;\n                    }\n                }[\"SelectContentImpl.useEffect.handlePointerUp\"];\n                if (triggerPointerDownPosRef.current !== null) {\n                    document.addEventListener(\"pointermove\", handlePointerMove);\n                    document.addEventListener(\"pointerup\", handlePointerUp, {\n                        capture: true,\n                        once: true\n                    });\n                }\n                return ({\n                    \"SelectContentImpl.useEffect\": ()=>{\n                        document.removeEventListener(\"pointermove\", handlePointerMove);\n                        document.removeEventListener(\"pointerup\", handlePointerUp, {\n                            capture: true\n                        });\n                    }\n                })[\"SelectContentImpl.useEffect\"];\n            }\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        content,\n        onOpenChange,\n        triggerPointerDownPosRef\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            const close = {\n                \"SelectContentImpl.useEffect.close\": ()=>onOpenChange(false)\n            }[\"SelectContentImpl.useEffect.close\"];\n            window.addEventListener(\"blur\", close);\n            window.addEventListener(\"resize\", close);\n            return ({\n                \"SelectContentImpl.useEffect\": ()=>{\n                    window.removeEventListener(\"blur\", close);\n                    window.removeEventListener(\"resize\", close);\n                }\n            })[\"SelectContentImpl.useEffect\"];\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        onOpenChange\n    ]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch({\n        \"SelectContentImpl.useTypeaheadSearch\": (search)=>{\n            const enabledItems = getItems().filter({\n                \"SelectContentImpl.useTypeaheadSearch.enabledItems\": (item)=>!item.disabled\n            }[\"SelectContentImpl.useTypeaheadSearch.enabledItems\"]);\n            const currentItem = enabledItems.find({\n                \"SelectContentImpl.useTypeaheadSearch.currentItem\": (item)=>item.ref.current === document.activeElement\n            }[\"SelectContentImpl.useTypeaheadSearch.currentItem\"]);\n            const nextItem = findNextItem(enabledItems, search, currentItem);\n            if (nextItem) {\n                setTimeout({\n                    \"SelectContentImpl.useTypeaheadSearch\": ()=>nextItem.ref.current.focus()\n                }[\"SelectContentImpl.useTypeaheadSearch\"]);\n            }\n        }\n    }[\"SelectContentImpl.useTypeaheadSearch\"]);\n    const itemRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[itemRefCallback]\": (node, value, disabled)=>{\n            const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n            const isSelectedItem = context.value !== void 0 && context.value === value;\n            if (isSelectedItem || isFirstValidItem) {\n                setSelectedItem(node);\n                if (isFirstValidItem) firstValidItemFoundRef.current = true;\n            }\n        }\n    }[\"SelectContentImpl.useCallback[itemRefCallback]\"], [\n        context.value\n    ]);\n    const handleItemLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[handleItemLeave]\": ()=>content?.focus()\n    }[\"SelectContentImpl.useCallback[handleItemLeave]\"], [\n        content\n    ]);\n    const itemTextRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[itemTextRefCallback]\": (node, value, disabled)=>{\n            const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n            const isSelectedItem = context.value !== void 0 && context.value === value;\n            if (isSelectedItem || isFirstValidItem) {\n                setSelectedItemText(node);\n            }\n        }\n    }[\"SelectContentImpl.useCallback[itemTextRefCallback]\"], [\n        context.value\n    ]);\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n        side,\n        sideOffset,\n        align,\n        alignOffset,\n        arrowPadding,\n        collisionBoundary,\n        collisionPadding,\n        sticky,\n        hideWhenDetached,\n        avoidCollisions\n    } : {};\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            as: Slot,\n            allowPinchZoom: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__.FocusScope, {\n                asChild: true,\n                trapped: context.open,\n                onMountAutoFocus: (event)=>{\n                    event.preventDefault();\n                },\n                onUnmountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(onCloseAutoFocus, (event)=>{\n                    context.trigger?.focus({\n                        preventScroll: true\n                    });\n                    event.preventDefault();\n                }),\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents: true,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside: (event)=>event.preventDefault(),\n                    onDismiss: ()=>context.onOpenChange(false),\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectPosition, {\n                        role: \"listbox\",\n                        id: context.contentId,\n                        \"data-state\": context.open ? \"open\" : \"closed\",\n                        dir: context.dir,\n                        onContextMenu: (event)=>event.preventDefault(),\n                        ...contentProps,\n                        ...popperContentProps,\n                        onPlaced: ()=>setIsPositioned(true),\n                        ref: composedRefs,\n                        style: {\n                            // flex layout so we can place the scroll buttons properly\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            // reset the outline by default as the content MAY get focused\n                            outline: \"none\",\n                            ...contentProps.style\n                        },\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                            if (event.key === \"Tab\") event.preventDefault();\n                            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                            if ([\n                                \"ArrowUp\",\n                                \"ArrowDown\",\n                                \"Home\",\n                                \"End\"\n                            ].includes(event.key)) {\n                                const items = getItems().filter((item)=>!item.disabled);\n                                let candidateNodes = items.map((item)=>item.ref.current);\n                                if ([\n                                    \"ArrowUp\",\n                                    \"End\"\n                                ].includes(event.key)) {\n                                    candidateNodes = candidateNodes.slice().reverse();\n                                }\n                                if ([\n                                    \"ArrowUp\",\n                                    \"ArrowDown\"\n                                ].includes(event.key)) {\n                                    const currentElement = event.target;\n                                    const currentIndex = candidateNodes.indexOf(currentElement);\n                                    candidateNodes = candidateNodes.slice(currentIndex + 1);\n                                }\n                                setTimeout(()=>focusFirst(candidateNodes));\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onPlaced, ...popperProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n    const [contentWrapper, setContentWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectItemAlignedPosition.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"SelectItemAlignedPosition.useComposedRefs[composedRefs]\"]);\n    const getItems = useCollection(__scopeSelect);\n    const shouldExpandOnScrollRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const shouldRepositionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n    const position = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectItemAlignedPosition.useCallback[position]\": ()=>{\n            if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n                const triggerRect = context.trigger.getBoundingClientRect();\n                const contentRect = content.getBoundingClientRect();\n                const valueNodeRect = context.valueNode.getBoundingClientRect();\n                const itemTextRect = selectedItemText.getBoundingClientRect();\n                if (context.dir !== \"rtl\") {\n                    const itemTextOffset = itemTextRect.left - contentRect.left;\n                    const left = valueNodeRect.left - itemTextOffset;\n                    const leftDelta = triggerRect.left - left;\n                    const minContentWidth = triggerRect.width + leftDelta;\n                    const contentWidth = Math.max(minContentWidth, contentRect.width);\n                    const rightEdge = window.innerWidth - CONTENT_MARGIN;\n                    const clampedLeft = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(left, [\n                        CONTENT_MARGIN,\n                        // Prevents the content from going off the starting edge of the\n                        // viewport. It may still go off the ending edge, but this can be\n                        // controlled by the user since they may want to manage overflow in a\n                        // specific way.\n                        // https://github.com/radix-ui/primitives/issues/2049\n                        Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n                    ]);\n                    contentWrapper.style.minWidth = minContentWidth + \"px\";\n                    contentWrapper.style.left = clampedLeft + \"px\";\n                } else {\n                    const itemTextOffset = contentRect.right - itemTextRect.right;\n                    const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n                    const rightDelta = window.innerWidth - triggerRect.right - right;\n                    const minContentWidth = triggerRect.width + rightDelta;\n                    const contentWidth = Math.max(minContentWidth, contentRect.width);\n                    const leftEdge = window.innerWidth - CONTENT_MARGIN;\n                    const clampedRight = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(right, [\n                        CONTENT_MARGIN,\n                        Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n                    ]);\n                    contentWrapper.style.minWidth = minContentWidth + \"px\";\n                    contentWrapper.style.right = clampedRight + \"px\";\n                }\n                const items = getItems();\n                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                const itemsHeight = viewport.scrollHeight;\n                const contentStyles = window.getComputedStyle(content);\n                const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n                const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n                const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n                const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n                const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n                const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n                const viewportStyles = window.getComputedStyle(viewport);\n                const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n                const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n                const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n                const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n                const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n                const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n                const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n                const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n                const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n                if (willAlignWithoutTopOverflow) {\n                    const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n                    contentWrapper.style.bottom = \"0px\";\n                    const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n                    const clampedTriggerMiddleToBottomEdge = Math.max(triggerMiddleToBottomEdge, selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n                    (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth);\n                    const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n                    contentWrapper.style.height = height + \"px\";\n                } else {\n                    const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n                    contentWrapper.style.top = \"0px\";\n                    const clampedTopEdgeToTriggerMiddle = Math.max(topEdgeToTriggerMiddle, contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n                    (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight);\n                    const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n                    contentWrapper.style.height = height + \"px\";\n                    viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n                }\n                contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n                contentWrapper.style.minHeight = minContentHeight + \"px\";\n                contentWrapper.style.maxHeight = availableHeight + \"px\";\n                onPlaced?.();\n                requestAnimationFrame({\n                    \"SelectItemAlignedPosition.useCallback[position]\": ()=>shouldExpandOnScrollRef.current = true\n                }[\"SelectItemAlignedPosition.useCallback[position]\"]);\n            }\n        }\n    }[\"SelectItemAlignedPosition.useCallback[position]\"], [\n        getItems,\n        context.trigger,\n        context.valueNode,\n        contentWrapper,\n        content,\n        viewport,\n        selectedItem,\n        selectedItemText,\n        context.dir,\n        onPlaced\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectItemAlignedPosition.useLayoutEffect\": ()=>position()\n    }[\"SelectItemAlignedPosition.useLayoutEffect\"], [\n        position\n    ]);\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectItemAlignedPosition.useLayoutEffect\": ()=>{\n            if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n        }\n    }[\"SelectItemAlignedPosition.useLayoutEffect\"], [\n        content\n    ]);\n    const handleScrollButtonChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectItemAlignedPosition.useCallback[handleScrollButtonChange]\": (node)=>{\n            if (node && shouldRepositionRef.current === true) {\n                position();\n                focusSelectedItem?.();\n                shouldRepositionRef.current = false;\n            }\n        }\n    }[\"SelectItemAlignedPosition.useCallback[handleScrollButtonChange]\"], [\n        position,\n        focusSelectedItem\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectViewportProvider, {\n        scope: __scopeSelect,\n        contentWrapper,\n        shouldExpandOnScrollRef,\n        onScrollButtonChange: handleScrollButtonChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n            ref: setContentWrapper,\n            style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                position: \"fixed\",\n                zIndex: contentZIndex\n            },\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                ...popperProps,\n                ref: composedRefs,\n                style: {\n                    // When we get the height of the content, it includes borders. If we were to set\n                    // the height without having `boxSizing: 'border-box'` it would be too big.\n                    boxSizing: \"border-box\",\n                    // We need to ensure the content doesn't get taller than the wrapper\n                    maxHeight: \"100%\",\n                    ...popperProps.style\n                }\n            })\n        })\n    });\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, align = \"start\", collisionPadding = CONTENT_MARGIN, ...popperProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Content, {\n        ...popperScope,\n        ...popperProps,\n        ref: forwardedRef,\n        align,\n        collisionPadding,\n        style: {\n            // Ensure border-box for floating-ui calculations\n            boxSizing: \"border-box\",\n            ...popperProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                    \"data-radix-select-viewport\": \"\",\n                    role: \"presentation\",\n                    ...viewportProps,\n                    ref: composedRefs,\n                    style: {\n                        // we use position: 'relative' here on the `viewport` so that when we call\n                        // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n                        // (independent of the scrollUpButton).\n                        position: \"relative\",\n                        flex: 1,\n                        // Viewport should only be scrollable in the vertical direction.\n                        // This won't work in vertical writing modes, so we'll need to\n                        // revisit this if/when that is supported\n                        // https://developer.chrome.com/blog/vertical-form-controls\n                        overflow: \"hidden auto\",\n                        ...viewportProps.style\n                    },\n                    onScroll: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(viewportProps.onScroll, (event)=>{\n                        const viewport = event.currentTarget;\n                        const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n                        if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                            const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                            if (scrolledBy > 0) {\n                                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                                const cssHeight = parseFloat(contentWrapper.style.height);\n                                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                                if (prevHeight < availableHeight) {\n                                    const nextHeight = prevHeight + scrolledBy;\n                                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                                    const heightDiff = nextHeight - clampedNextHeight;\n                                    contentWrapper.style.height = clampedNextHeight + \"px\";\n                                    if (contentWrapper.style.bottom === \"0px\") {\n                                        viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                                        contentWrapper.style.justifyContent = \"flex-end\";\n                                    }\n                                }\n                            }\n                        }\n                        prevScrollTopRef.current = viewport.scrollTop;\n                    })\n                })\n            })\n        ]\n    });\n});\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectGroupContextProvider, {\n        scope: __scopeSelect,\n        id: groupId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n            role: \"group\",\n            \"aria-labelledby\": groupId,\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        id: groupContext.id,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, value, disabled = false, textValue: textValueProp, ...itemProps } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectItem.useComposedRefs[composedRefs]\": (node)=>contentContext.itemRefCallback?.(node, value, disabled)\n    }[\"SelectItem.useComposedRefs[composedRefs]\"]);\n    const textId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const handleSelect = ()=>{\n        if (!disabled) {\n            context.onValueChange(value);\n            context.onOpenChange(false);\n        }\n    };\n    if (value === \"\") {\n        throw new Error(\"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\");\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectItemContextProvider, {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"SelectItem.useCallback\": (node)=>{\n                setTextValue({\n                    \"SelectItem.useCallback\": (prevTextValue)=>prevTextValue || (node?.textContent ?? \"\").trim()\n                }[\"SelectItem.useCallback\"]);\n            }\n        }[\"SelectItem.useCallback\"], []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onBlur, ()=>setIsFocused(false)),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onClick, ()=>{\n                    if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerUp, ()=>{\n                    if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerDown, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerMove, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                    if (disabled) {\n                        contentContext.onItemLeave?.();\n                    } else if (pointerTypeRef.current === \"mouse\") {\n                        event.currentTarget.focus({\n                            preventScroll: true\n                        });\n                    }\n                }),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerLeave, (event)=>{\n                    if (event.currentTarget === document.activeElement) {\n                        contentContext.onItemLeave?.();\n                    }\n                }),\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onKeyDown, (event)=>{\n                    const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                    if (isTypingAhead && event.key === \" \") return;\n                    if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                    if (event.key === \" \") event.preventDefault();\n                })\n            })\n        })\n    });\n});\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectItemText.useComposedRefs[composedRefs]\": (node)=>setItemTextNode(node)\n    }[\"SelectItemText.useComposedRefs[composedRefs]\"], itemContext.onItemTextChange, {\n        \"SelectItemText.useComposedRefs[composedRefs]\": (node)=>contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    }[\"SelectItemText.useComposedRefs[composedRefs]\"]);\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"SelectItemText.useMemo[nativeOption]\": ()=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                value: itemContext.value,\n                disabled: itemContext.disabled,\n                children: textContent\n            }, itemContext.value)\n    }[\"SelectItemText.useMemo[nativeOption]\"], [\n        itemContext.disabled,\n        itemContext.value,\n        textContent\n    ]);\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectItemText.useLayoutEffect\": ()=>{\n            onNativeOptionAdd(nativeOption);\n            return ({\n                \"SelectItemText.useLayoutEffect\": ()=>onNativeOptionRemove(nativeOption)\n            })[\"SelectItemText.useLayoutEffect\"];\n        }\n    }[\"SelectItemText.useLayoutEffect\"], [\n        onNativeOptionAdd,\n        onNativeOptionRemove,\n        nativeOption\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n                id: itemContext.textId,\n                ...itemTextProps,\n                ref: composedRefs\n            }),\n            itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(itemTextProps.children, context.valueNode) : null\n        ]\n    });\n});\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollUp, setCanScrollUp] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectScrollUpButton.useLayoutEffect\": ()=>{\n            if (contentContext.viewport && contentContext.isPositioned) {\n                let handleScroll2 = {\n                    \"SelectScrollUpButton.useLayoutEffect.handleScroll2\": function() {\n                        const canScrollUp2 = viewport.scrollTop > 0;\n                        setCanScrollUp(canScrollUp2);\n                    }\n                }[\"SelectScrollUpButton.useLayoutEffect.handleScroll2\"];\n                var handleScroll = handleScroll2;\n                const viewport = contentContext.viewport;\n                handleScroll2();\n                viewport.addEventListener(\"scroll\", handleScroll2);\n                return ({\n                    \"SelectScrollUpButton.useLayoutEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll2)\n                })[\"SelectScrollUpButton.useLayoutEffect\"];\n            }\n        }\n    }[\"SelectScrollUpButton.useLayoutEffect\"], [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollUp ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollDown, setCanScrollDown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectScrollDownButton.useLayoutEffect\": ()=>{\n            if (contentContext.viewport && contentContext.isPositioned) {\n                let handleScroll2 = {\n                    \"SelectScrollDownButton.useLayoutEffect.handleScroll2\": function() {\n                        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n                        const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n                        setCanScrollDown(canScrollDown2);\n                    }\n                }[\"SelectScrollDownButton.useLayoutEffect.handleScroll2\"];\n                var handleScroll = handleScroll2;\n                const viewport = contentContext.viewport;\n                handleScroll2();\n                viewport.addEventListener(\"scroll\", handleScroll2);\n                return ({\n                    \"SelectScrollDownButton.useLayoutEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll2)\n                })[\"SelectScrollDownButton.useLayoutEffect\"];\n            }\n        }\n    }[\"SelectScrollDownButton.useLayoutEffect\"], [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollDown ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n    const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n    const autoScrollTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const getItems = useCollection(__scopeSelect);\n    const clearAutoScrollTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectScrollButtonImpl.useCallback[clearAutoScrollTimer]\": ()=>{\n            if (autoScrollTimerRef.current !== null) {\n                window.clearInterval(autoScrollTimerRef.current);\n                autoScrollTimerRef.current = null;\n            }\n        }\n    }[\"SelectScrollButtonImpl.useCallback[clearAutoScrollTimer]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectScrollButtonImpl.useEffect\": ()=>{\n            return ({\n                \"SelectScrollButtonImpl.useEffect\": ()=>clearAutoScrollTimer()\n            })[\"SelectScrollButtonImpl.useEffect\"];\n        }\n    }[\"SelectScrollButtonImpl.useEffect\"], [\n        clearAutoScrollTimer\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectScrollButtonImpl.useLayoutEffect\": ()=>{\n            const activeItem = getItems().find({\n                \"SelectScrollButtonImpl.useLayoutEffect.activeItem\": (item)=>item.ref.current === document.activeElement\n            }[\"SelectScrollButtonImpl.useLayoutEffect.activeItem\"]);\n            activeItem?.ref.current?.scrollIntoView({\n                block: \"nearest\"\n            });\n        }\n    }[\"SelectScrollButtonImpl.useLayoutEffect\"], [\n        getItems\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...scrollIndicatorProps,\n        ref: forwardedRef,\n        style: {\n            flexShrink: 0,\n            ...scrollIndicatorProps.style\n        },\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerDown, ()=>{\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerMove, ()=>{\n            contentContext.onItemLeave?.();\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerLeave, ()=>{\n            clearAutoScrollTimer();\n        })\n    });\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectArrow.displayName = ARROW_NAME;\nvar BUBBLE_INPUT_NAME = \"SelectBubbleInput\";\nvar SelectBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeSelect, value, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, ref);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectBubbleInput.useEffect\": ()=>{\n            const select = ref.current;\n            if (!select) return;\n            const selectProto = window.HTMLSelectElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(selectProto, \"value\");\n            const setValue = descriptor.set;\n            if (prevValue !== value && setValue) {\n                const event = new Event(\"change\", {\n                    bubbles: true\n                });\n                setValue.call(select, value);\n                select.dispatchEvent(event);\n            }\n        }\n    }[\"SelectBubbleInput.useEffect\"], [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.select, {\n        ...props,\n        style: {\n            ..._radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__.VISUALLY_HIDDEN_STYLES,\n            ...props.style\n        },\n        ref: composedRefs,\n        defaultValue: value\n    });\n});\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction shouldShowPlaceholder(value) {\n    return value === \"\" || value === void 0;\n}\nfunction useTypeaheadSearch(onSearchChange) {\n    const handleSearchChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__.useCallbackRef)(onSearchChange);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const handleTypeaheadSearch = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useTypeaheadSearch.useCallback[handleTypeaheadSearch]\": (key)=>{\n            const search = searchRef.current + key;\n            handleSearchChange(search);\n            (function updateSearch(value) {\n                searchRef.current = value;\n                window.clearTimeout(timerRef.current);\n                if (value !== \"\") timerRef.current = window.setTimeout({\n                    \"useTypeaheadSearch.useCallback[handleTypeaheadSearch].updateSearch\": ()=>updateSearch(\"\")\n                }[\"useTypeaheadSearch.useCallback[handleTypeaheadSearch].updateSearch\"], 1e3);\n            })(search);\n        }\n    }[\"useTypeaheadSearch.useCallback[handleTypeaheadSearch]\"], [\n        handleSearchChange\n    ]);\n    const resetTypeahead = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useTypeaheadSearch.useCallback[resetTypeahead]\": ()=>{\n            searchRef.current = \"\";\n            window.clearTimeout(timerRef.current);\n        }\n    }[\"useTypeaheadSearch.useCallback[resetTypeahead]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useTypeaheadSearch.useEffect\": ()=>{\n            return ({\n                \"useTypeaheadSearch.useEffect\": ()=>window.clearTimeout(timerRef.current)\n            })[\"useTypeaheadSearch.useEffect\"];\n        }\n    }[\"useTypeaheadSearch.useEffect\"], []);\n    return [\n        searchRef,\n        handleTypeaheadSearch,\n        resetTypeahead\n    ];\n}\nfunction findNextItem(items, search, currentItem) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n    let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n    const excludeCurrentItem = normalizedSearch.length === 1;\n    if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v)=>v !== currentItem);\n    const nextItem = wrappedItems.find((item)=>item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-select@2.2._cfc66dd1b0ece16c03e8c586bc72e264/node_modules/@radix-ui/react-select/dist/index.mjs\n");

/***/ })

};
;