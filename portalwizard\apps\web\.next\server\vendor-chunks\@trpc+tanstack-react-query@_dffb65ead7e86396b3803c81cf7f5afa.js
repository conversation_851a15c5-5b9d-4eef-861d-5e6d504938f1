"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa";
exports.ids = ["vendor-chunks/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/index.mjs":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/index.mjs ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTRPCContext: () => (/* reexport safe */ _internals_Context_mjs__WEBPACK_IMPORTED_MODULE_0__.createTRPCContext),\n/* harmony export */   createTRPCOptionsProxy: () => (/* reexport safe */ _internals_createOptionsProxy_mjs__WEBPACK_IMPORTED_MODULE_1__.createTRPCOptionsProxy),\n/* harmony export */   useSubscription: () => (/* reexport safe */ _internals_subscriptionOptions_mjs__WEBPACK_IMPORTED_MODULE_2__.useSubscription)\n/* harmony export */ });\n/* harmony import */ var _internals_Context_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internals/Context.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/Context.mjs\");\n/* harmony import */ var _internals_createOptionsProxy_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./internals/createOptionsProxy.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/createOptionsProxy.mjs\");\n/* harmony import */ var _internals_subscriptionOptions_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./internals/subscriptionOptions.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/subscriptionOptions.mjs\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0cnBjK3RhbnN0YWNrLXJlYWN0LXF1ZXJ5QF9kZmZiNjVlYWQ3ZTg2Mzk2YjM4MDNjODFjZjdmNWFmYS9ub2RlX21vZHVsZXMvQHRycGMvdGFuc3RhY2stcmVhY3QtcXVlcnkvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTREO0FBQ2dCO0FBQ04iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUiAxXFxEZXNrdG9wXFxkZXZvdHNwLXYxLjVcXHBvcnRhbHdpemFyZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHRycGMrdGFuc3RhY2stcmVhY3QtcXVlcnlAX2RmZmI2NWVhZDdlODYzOTZiMzgwM2M4MWNmN2Y1YWZhXFxub2RlX21vZHVsZXNcXEB0cnBjXFx0YW5zdGFjay1yZWFjdC1xdWVyeVxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGNyZWF0ZVRSUENDb250ZXh0IH0gZnJvbSAnLi9pbnRlcm5hbHMvQ29udGV4dC5tanMnO1xuZXhwb3J0IHsgY3JlYXRlVFJQQ09wdGlvbnNQcm94eSB9IGZyb20gJy4vaW50ZXJuYWxzL2NyZWF0ZU9wdGlvbnNQcm94eS5tanMnO1xuZXhwb3J0IHsgdXNlU3Vic2NyaXB0aW9uIH0gZnJvbSAnLi9pbnRlcm5hbHMvc3Vic2NyaXB0aW9uT3B0aW9ucy5tanMnO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/Context.mjs":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/Context.mjs ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTRPCContext: () => (/* binding */ createTRPCContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _createOptionsProxy_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createOptionsProxy.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/createOptionsProxy.mjs\");\n\n\n\n/**\n * Create a set of type-safe provider-consumers\n *\n * @see https://trpc.io/docs/client/tanstack-react-query/setup#3a-setup-the-trpc-context-provider\n */ function createTRPCContext() {\n    const TRPCClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n    const TRPCContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n    function TRPCProvider(props) {\n        const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(0,_createOptionsProxy_mjs__WEBPACK_IMPORTED_MODULE_1__.createTRPCOptionsProxy)({\n                client: props.trpcClient,\n                queryClient: props.queryClient\n            }), [\n            props.trpcClient,\n            props.queryClient\n        ]);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(TRPCClientContext.Provider, {\n            value: props.trpcClient\n        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(TRPCContext.Provider, {\n            value: value\n        }, props.children));\n    }\n    function useTRPC() {\n        const utils = react__WEBPACK_IMPORTED_MODULE_0__.useContext(TRPCContext);\n        if (!utils) {\n            throw new Error('useTRPC() can only be used inside of a <TRPCProvider>');\n        }\n        return utils;\n    }\n    function useTRPCClient() {\n        const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(TRPCClientContext);\n        if (!client) {\n            throw new Error('useTRPCClient() can only be used inside of a <TRPCProvider>');\n        }\n        return client;\n    }\n    return {\n        TRPCProvider,\n        useTRPC,\n        useTRPCClient\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/Context.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/createOptionsProxy.mjs":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/createOptionsProxy.mjs ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTRPCOptionsProxy: () => (/* binding */ createTRPCOptionsProxy)\n/* harmony export */ });\n/* harmony import */ var _trpc_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/client */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/index.mjs\");\n/* harmony import */ var _trpc_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @trpc/server */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/index.mjs\");\n/* harmony import */ var _infiniteQueryOptions_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./infiniteQueryOptions.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/infiniteQueryOptions.mjs\");\n/* harmony import */ var _mutationOptions_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mutationOptions.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/mutationOptions.mjs\");\n/* harmony import */ var _queryOptions_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./queryOptions.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/queryOptions.mjs\");\n/* harmony import */ var _subscriptionOptions_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./subscriptionOptions.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/subscriptionOptions.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/utils.mjs\");\n\n\n\n\n\n\n\n\n/**\n * Create a typed proxy from your router types. Can also be used on the server.\n *\n * @see https://trpc.io/docs/client/tanstack-react-query/setup#3b-setup-without-react-context\n * @see https://trpc.io/docs/client/tanstack-react-query/server-components#5-create-a-trpc-caller-for-server-components\n */ function createTRPCOptionsProxy(opts) {\n    const callIt = (type)=>{\n        return (path, input, trpcOpts)=>{\n            if ('router' in opts) {\n                return Promise.resolve((0,_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.unwrapLazyArg)(opts.ctx)).then((ctx)=>(0,_trpc_server__WEBPACK_IMPORTED_MODULE_1__.callTRPCProcedure)({\n                        router: opts.router,\n                        path: path,\n                        getRawInput: async ()=>input,\n                        ctx: ctx,\n                        type: type,\n                        signal: undefined\n                    }));\n            }\n            const untypedClient = opts.client instanceof _trpc_client__WEBPACK_IMPORTED_MODULE_0__.TRPCUntypedClient ? opts.client : (0,_trpc_client__WEBPACK_IMPORTED_MODULE_0__.getUntypedClient)(opts.client);\n            return untypedClient[type](path, input, trpcOpts);\n        };\n    };\n    return (0,_trpc_server__WEBPACK_IMPORTED_MODULE_1__.createTRPCRecursiveProxy)(({ args, path: _path })=>{\n        const path = [\n            ..._path\n        ];\n        const utilName = path.pop();\n        const [arg1, arg2] = args;\n        const contextMap = {\n            '~types': undefined,\n            pathKey: ()=>{\n                return (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.getQueryKeyInternal)(path);\n            },\n            pathFilter: ()=>{\n                return {\n                    ...arg1,\n                    queryKey: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.getQueryKeyInternal)(path)\n                };\n            },\n            queryOptions: ()=>{\n                return (0,_queryOptions_mjs__WEBPACK_IMPORTED_MODULE_4__.trpcQueryOptions)({\n                    input: arg1,\n                    opts: arg2,\n                    path,\n                    queryClient: opts.queryClient,\n                    queryKey: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.getQueryKeyInternal)(path, arg1, 'query'),\n                    query: callIt('query')\n                });\n            },\n            queryKey: ()=>{\n                return (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.getQueryKeyInternal)(path, arg1, 'query');\n            },\n            queryFilter: ()=>{\n                return {\n                    ...arg2,\n                    queryKey: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.getQueryKeyInternal)(path, arg1, 'query')\n                };\n            },\n            infiniteQueryOptions: ()=>{\n                return (0,_infiniteQueryOptions_mjs__WEBPACK_IMPORTED_MODULE_2__.trpcInfiniteQueryOptions)({\n                    input: arg1,\n                    opts: arg2,\n                    path,\n                    queryClient: opts.queryClient,\n                    queryKey: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.getQueryKeyInternal)(path, arg1, 'infinite'),\n                    query: callIt('query')\n                });\n            },\n            infiniteQueryKey: ()=>{\n                return (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.getQueryKeyInternal)(path, arg1, 'infinite');\n            },\n            infiniteQueryFilter: ()=>{\n                return {\n                    ...arg2,\n                    queryKey: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.getQueryKeyInternal)(path, arg1, 'infinite')\n                };\n            },\n            mutationOptions: ()=>{\n                return (0,_mutationOptions_mjs__WEBPACK_IMPORTED_MODULE_3__.trpcMutationOptions)({\n                    opts: arg1,\n                    path,\n                    queryClient: opts.queryClient,\n                    mutate: callIt('mutation'),\n                    overrides: opts.overrides?.mutations\n                });\n            },\n            mutationKey: ()=>{\n                return (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.getMutationKeyInternal)(path);\n            },\n            subscriptionOptions: ()=>{\n                return (0,_subscriptionOptions_mjs__WEBPACK_IMPORTED_MODULE_5__.trpcSubscriptionOptions)({\n                    opts: arg2,\n                    path,\n                    queryKey: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_6__.getQueryKeyInternal)(path, arg1, 'any'),\n                    subscribe: callIt('subscription')\n                });\n            }\n        };\n        return contextMap[utilName]();\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/createOptionsProxy.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/infiniteQueryOptions.mjs":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/infiniteQueryOptions.mjs ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trpcInfiniteQueryOptions: () => (/* binding */ trpcInfiniteQueryOptions)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.75.7/node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.75.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/infiniteQueryOptions.js\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/utils.mjs\");\n\n\n\nfunction trpcInfiniteQueryOptions(args) {\n    const { input, query, path, queryKey, opts } = args;\n    const inputIsSkipToken = input === _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.skipToken;\n    const queryFn = async (queryFnContext)=>{\n        const actualOpts = {\n            ...opts,\n            trpc: {\n                ...opts?.trpc,\n                ...opts?.trpc?.abortOnUnmount ? {\n                    signal: queryFnContext.signal\n                } : {\n                    signal: null\n                }\n            }\n        };\n        const result = await query(...(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.getClientArgs)(queryKey, actualOpts, {\n            direction: queryFnContext.direction,\n            pageParam: queryFnContext.pageParam\n        }));\n        return result;\n    };\n    return Object.assign((0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.infiniteQueryOptions)({\n        ...opts,\n        queryKey,\n        queryFn: inputIsSkipToken ? _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.skipToken : queryFn,\n        initialPageParam: opts?.initialCursor ?? input?.cursor\n    }), {\n        trpc: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.createTRPCOptionsResult)({\n            path\n        })\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/infiniteQueryOptions.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/mutationOptions.mjs":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/mutationOptions.mjs ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trpcMutationOptions: () => (/* binding */ trpcMutationOptions)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/utils.mjs\");\n\n\n/**\n * @internal\n */ function trpcMutationOptions(args) {\n    const { mutate, path, opts, overrides } = args;\n    const queryClient = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.unwrapLazyArg)(args.queryClient);\n    const mutationKey = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.getMutationKeyInternal)(path);\n    const defaultOpts = queryClient.defaultMutationOptions(queryClient.getMutationDefaults(mutationKey));\n    const mutationSuccessOverride = overrides?.onSuccess ?? ((options)=>options.originalFn());\n    const mutationFn = async (input)=>{\n        const result = await mutate(...(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.getClientArgs)([\n            path,\n            {\n                input\n            }\n        ], opts));\n        return result;\n    };\n    return {\n        ...opts,\n        mutationKey: mutationKey,\n        mutationFn,\n        onSuccess (...args) {\n            const originalFn = ()=>opts?.onSuccess?.(...args) ?? defaultOpts?.onSuccess?.(...args);\n            return mutationSuccessOverride({\n                originalFn,\n                queryClient,\n                meta: opts?.meta ?? defaultOpts?.meta ?? {}\n            });\n        },\n        trpc: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.createTRPCOptionsResult)({\n            path\n        })\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/mutationOptions.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/queryOptions.mjs":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/queryOptions.mjs ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trpcQueryOptions: () => (/* binding */ trpcQueryOptions)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.75.7/node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.75.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/queryOptions.js\");\n/* harmony import */ var _trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/unstable-core-do-not-import */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/unstable-core-do-not-import.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/utils.mjs\");\n\n\n\n\n/**\n * @internal\n */ function trpcQueryOptions(args) {\n    const { input, query, path, queryKey, opts } = args;\n    const queryClient = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.unwrapLazyArg)(args.queryClient);\n    const inputIsSkipToken = input === _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.skipToken;\n    const queryFn = async (queryFnContext)=>{\n        const actualOpts = {\n            ...opts,\n            trpc: {\n                ...opts?.trpc,\n                ...opts?.trpc?.abortOnUnmount ? {\n                    signal: queryFnContext.signal\n                } : {\n                    signal: null\n                }\n            }\n        };\n        const result = await query(...(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.getClientArgs)(queryKey, actualOpts));\n        if ((0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_0__.isAsyncIterable)(result)) {\n            return (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.buildQueryFromAsyncIterable)(result, queryClient, queryKey);\n        }\n        return result;\n    };\n    return Object.assign((0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.queryOptions)({\n        ...opts,\n        queryKey,\n        queryFn: inputIsSkipToken ? _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.skipToken : queryFn\n    }), {\n        trpc: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.createTRPCOptionsResult)({\n            path\n        })\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/queryOptions.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/subscriptionOptions.mjs":
/*!****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/subscriptionOptions.mjs ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trpcSubscriptionOptions: () => (/* binding */ trpcSubscriptionOptions),\n/* harmony export */   useSubscription: () => (/* binding */ useSubscription)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.75.7/node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/utils.mjs\");\n\n\n\n\n/**\n * @internal\n */ const trpcSubscriptionOptions = (args)=>{\n    const { subscribe, path, queryKey, opts = {} } = args;\n    const input = queryKey[1]?.input;\n    const enabled = 'enabled' in opts ? !!opts.enabled : input !== _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.skipToken;\n    const _subscribe = (innerOpts)=>{\n        return subscribe(path.join('.'), input ?? undefined, innerOpts);\n    };\n    return {\n        ...opts,\n        enabled,\n        subscribe: _subscribe,\n        queryKey,\n        trpc: (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.createTRPCOptionsResult)({\n            path\n        })\n    };\n};\nfunction useSubscription(opts) {\n    const optsRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(opts);\n    optsRef.current = opts;\n    const trackedProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(new Set([]));\n    const addTrackedProp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((key)=>{\n        trackedProps.current.add(key);\n    }, []);\n    const currentSubscriptionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{\n    // noop\n    });\n    const reset = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        // unsubscribe from the previous subscription\n        currentSubscriptionRef.current?.();\n        updateState(getInitialState);\n        if (!opts.enabled) {\n            return;\n        }\n        const subscription = opts.subscribe({\n            onStarted: ()=>{\n                optsRef.current.onStarted?.();\n                updateState((prev)=>({\n                        ...prev,\n                        status: 'pending',\n                        error: null\n                    }));\n            },\n            onData: (data)=>{\n                optsRef.current.onData?.(data);\n                updateState((prev)=>({\n                        ...prev,\n                        status: 'pending',\n                        data,\n                        error: null\n                    }));\n            },\n            onError: (error)=>{\n                optsRef.current.onError?.(error);\n                updateState((prev)=>({\n                        ...prev,\n                        status: 'error',\n                        error\n                    }));\n            },\n            onConnectionStateChange: (result)=>{\n                updateState((prev)=>{\n                    switch(result.state){\n                        case 'connecting':\n                            return {\n                                ...prev,\n                                status: 'connecting',\n                                error: result.error\n                            };\n                        case 'pending':\n                            // handled in onStarted\n                            return prev;\n                        case 'idle':\n                            return {\n                                ...prev,\n                                status: 'idle',\n                                data: undefined,\n                                error: null\n                            };\n                    }\n                });\n            }\n        });\n        currentSubscriptionRef.current = ()=>{\n            subscription.unsubscribe();\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.hashKey)(opts.queryKey),\n        opts.enabled\n    ]);\n    const getInitialState = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        return opts.enabled ? {\n            data: undefined,\n            error: null,\n            status: 'connecting',\n            reset\n        } : {\n            data: undefined,\n            error: null,\n            status: 'idle',\n            reset\n        };\n    }, [\n        opts.enabled,\n        reset\n    ]);\n    const resultRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(getInitialState());\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(trackResult(resultRef.current, addTrackedProp));\n    state.reset = reset;\n    const updateState = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((callback)=>{\n        const prev = resultRef.current;\n        const next = resultRef.current = callback(prev);\n        let shouldUpdate = false;\n        for (const key of trackedProps.current){\n            if (prev[key] !== next[key]) {\n                shouldUpdate = true;\n                break;\n            }\n        }\n        if (shouldUpdate) {\n            setState(trackResult(next, addTrackedProp));\n        }\n    }, [\n        addTrackedProp\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!opts.enabled) {\n            return;\n        }\n        reset();\n        return ()=>{\n            currentSubscriptionRef.current?.();\n        };\n    }, [\n        reset,\n        opts.enabled\n    ]);\n    return state;\n}\nfunction trackResult(result, onTrackResult) {\n    const trackedResult = new Proxy(result, {\n        get (target, prop) {\n            onTrackResult(prop);\n            return target[prop];\n        }\n    });\n    return trackedResult;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/subscriptionOptions.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/utils.mjs":
/*!**************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/utils.mjs ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildQueryFromAsyncIterable: () => (/* binding */ buildQueryFromAsyncIterable),\n/* harmony export */   createTRPCOptionsResult: () => (/* binding */ createTRPCOptionsResult),\n/* harmony export */   getClientArgs: () => (/* binding */ getClientArgs),\n/* harmony export */   getMutationKeyInternal: () => (/* binding */ getMutationKeyInternal),\n/* harmony export */   getQueryKeyInternal: () => (/* binding */ getQueryKeyInternal),\n/* harmony export */   unwrapLazyArg: () => (/* binding */ unwrapLazyArg)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.75.7/node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/unstable-core-do-not-import */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/unstable-core-do-not-import.mjs\");\n\n\n\n/**\n * @internal\n */ function createTRPCOptionsResult(value) {\n    const path = value.path.join('.');\n    return {\n        path\n    };\n}\n/**\n * @internal\n */ function getClientArgs(queryKey, opts, infiniteParams) {\n    const path = queryKey[0];\n    let input = queryKey[1]?.input;\n    if (infiniteParams) {\n        input = {\n            ...input ?? {},\n            ...infiniteParams.pageParam !== undefined ? {\n                cursor: infiniteParams.pageParam\n            } : {},\n            direction: infiniteParams.direction\n        };\n    }\n    return [\n        path.join('.'),\n        input,\n        opts?.trpc\n    ];\n}\n/**\n * @internal\n */ async function buildQueryFromAsyncIterable(asyncIterable, queryClient, queryKey) {\n    const queryCache = queryClient.getQueryCache();\n    const query = queryCache.build(queryClient, {\n        queryKey\n    });\n    query.setState({\n        data: [],\n        status: 'success'\n    });\n    const aggregate = [];\n    for await (const value of asyncIterable){\n        aggregate.push(value);\n        query.setState({\n            data: [\n                ...aggregate\n            ]\n        });\n    }\n    return aggregate;\n}\n/**\n * To allow easy interactions with groups of related queries, such as\n * invalidating all queries of a router, we use an array as the path when\n * storing in tanstack query.\n *\n * @internal\n */ function getQueryKeyInternal(path, input, type) {\n    // Construct a query key that is easy to destructure and flexible for\n    // partial selecting etc.\n    // https://github.com/trpc/trpc/issues/3128\n    // some parts of the path may be dot-separated, split them up\n    const splitPath = path.flatMap((part)=>part.split('.'));\n    if (!input && (!type || type === 'any')) {\n        // this matches also all mutations (see `getMutationKeyInternal`)\n        // for `utils.invalidate()` to match all queries (including vanilla react-query)\n        // we don't want nested array if path is empty, i.e. `[]` instead of `[[]]`\n        return splitPath.length ? [\n            splitPath\n        ] : [];\n    }\n    if (type === 'infinite' && (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_0__.isObject)(input) && ('direction' in input || 'cursor' in input)) {\n        const { cursor: _, direction: __, ...inputWithoutCursorAndDirection } = input;\n        return [\n            splitPath,\n            {\n                input: inputWithoutCursorAndDirection,\n                type: 'infinite'\n            }\n        ];\n    }\n    return [\n        splitPath,\n        {\n            ...typeof input !== 'undefined' && input !== _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.skipToken && {\n                input: input\n            },\n            ...type && type !== 'any' && {\n                type: type\n            }\n        }\n    ];\n}\n/**\n * @internal\n */ function getMutationKeyInternal(path) {\n    // some parts of the path may be dot-separated, split them up\n    const splitPath = path.flatMap((part)=>part.split('.'));\n    return splitPath.length ? [\n        splitPath\n    ] : [];\n}\n/**\n * @internal\n */ function unwrapLazyArg(valueOrLazy) {\n    return (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_0__.isFunction)(valueOrLazy) ? valueOrLazy() : valueOrLazy;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+tanstack-react-query@_dffb65ead7e86396b3803c81cf7f5afa/node_modules/@trpc/tanstack-react-query/dist/internals/utils.mjs\n");

/***/ })

};
;