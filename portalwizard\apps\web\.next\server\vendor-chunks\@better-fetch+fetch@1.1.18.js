"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@better-fetch+fetch@1.1.18";
exports.ids = ["vendor-chunks/@better-fetch+fetch@1.1.18"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@better-fetch+fetch@1.1.18/node_modules/@better-fetch/fetch/dist/index.js":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@better-fetch+fetch@1.1.18/node_modules/@better-fetch/fetch/dist/index.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BetterFetchError: () => (/* binding */ BetterFetchError),\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError),\n/* harmony export */   applySchemaPlugin: () => (/* binding */ applySchemaPlugin),\n/* harmony export */   betterFetch: () => (/* binding */ betterFetch),\n/* harmony export */   bodyParser: () => (/* binding */ bodyParser),\n/* harmony export */   createFetch: () => (/* binding */ createFetch),\n/* harmony export */   createRetryStrategy: () => (/* binding */ createRetryStrategy),\n/* harmony export */   createSchema: () => (/* binding */ createSchema),\n/* harmony export */   detectContentType: () => (/* binding */ detectContentType),\n/* harmony export */   detectResponseType: () => (/* binding */ detectResponseType),\n/* harmony export */   getBody: () => (/* binding */ getBody),\n/* harmony export */   getFetch: () => (/* binding */ getFetch),\n/* harmony export */   getHeaders: () => (/* binding */ getHeaders),\n/* harmony export */   getMethod: () => (/* binding */ getMethod),\n/* harmony export */   getTimeout: () => (/* binding */ getTimeout),\n/* harmony export */   getURL: () => (/* binding */ getURL),\n/* harmony export */   initializePlugins: () => (/* binding */ initializePlugins),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isJSONParsable: () => (/* binding */ isJSONParsable),\n/* harmony export */   isJSONSerializable: () => (/* binding */ isJSONSerializable),\n/* harmony export */   isPayloadMethod: () => (/* binding */ isPayloadMethod),\n/* harmony export */   isRouteMethod: () => (/* binding */ isRouteMethod),\n/* harmony export */   jsonParse: () => (/* binding */ jsonParse),\n/* harmony export */   methods: () => (/* binding */ methods),\n/* harmony export */   parseStandardSchema: () => (/* binding */ parseStandardSchema)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\n\n// src/error.ts\nvar BetterFetchError = class extends Error {\n  constructor(status, statusText, error) {\n    super(statusText || status.toString(), {\n      cause: error\n    });\n    this.status = status;\n    this.statusText = statusText;\n    this.error = error;\n  }\n};\n\n// src/plugins.ts\nvar initializePlugins = async (url, options) => {\n  var _a, _b, _c, _d, _e, _f;\n  let opts = options || {};\n  const hooks = {\n    onRequest: [options == null ? void 0 : options.onRequest],\n    onResponse: [options == null ? void 0 : options.onResponse],\n    onSuccess: [options == null ? void 0 : options.onSuccess],\n    onError: [options == null ? void 0 : options.onError],\n    onRetry: [options == null ? void 0 : options.onRetry]\n  };\n  if (!options || !(options == null ? void 0 : options.plugins)) {\n    return {\n      url,\n      options: opts,\n      hooks\n    };\n  }\n  for (const plugin of (options == null ? void 0 : options.plugins) || []) {\n    if (plugin.init) {\n      const pluginRes = await ((_a = plugin.init) == null ? void 0 : _a.call(plugin, url.toString(), options));\n      opts = pluginRes.options || opts;\n      url = pluginRes.url;\n    }\n    hooks.onRequest.push((_b = plugin.hooks) == null ? void 0 : _b.onRequest);\n    hooks.onResponse.push((_c = plugin.hooks) == null ? void 0 : _c.onResponse);\n    hooks.onSuccess.push((_d = plugin.hooks) == null ? void 0 : _d.onSuccess);\n    hooks.onError.push((_e = plugin.hooks) == null ? void 0 : _e.onError);\n    hooks.onRetry.push((_f = plugin.hooks) == null ? void 0 : _f.onRetry);\n  }\n  return {\n    url,\n    options: opts,\n    hooks\n  };\n};\n\n// src/retry.ts\nvar LinearRetryStrategy = class {\n  constructor(options) {\n    this.options = options;\n  }\n  shouldAttemptRetry(attempt, response) {\n    if (this.options.shouldRetry) {\n      return Promise.resolve(\n        attempt < this.options.attempts && this.options.shouldRetry(response)\n      );\n    }\n    return Promise.resolve(attempt < this.options.attempts);\n  }\n  getDelay() {\n    return this.options.delay;\n  }\n};\nvar ExponentialRetryStrategy = class {\n  constructor(options) {\n    this.options = options;\n  }\n  shouldAttemptRetry(attempt, response) {\n    if (this.options.shouldRetry) {\n      return Promise.resolve(\n        attempt < this.options.attempts && this.options.shouldRetry(response)\n      );\n    }\n    return Promise.resolve(attempt < this.options.attempts);\n  }\n  getDelay(attempt) {\n    const delay = Math.min(\n      this.options.maxDelay,\n      this.options.baseDelay * 2 ** attempt\n    );\n    return delay;\n  }\n};\nfunction createRetryStrategy(options) {\n  if (typeof options === \"number\") {\n    return new LinearRetryStrategy({\n      type: \"linear\",\n      attempts: options,\n      delay: 1e3\n    });\n  }\n  switch (options.type) {\n    case \"linear\":\n      return new LinearRetryStrategy(options);\n    case \"exponential\":\n      return new ExponentialRetryStrategy(options);\n    default:\n      throw new Error(\"Invalid retry strategy\");\n  }\n}\n\n// src/auth.ts\nvar getAuthHeader = async (options) => {\n  const headers = {};\n  const getValue = async (value) => typeof value === \"function\" ? await value() : value;\n  if (options == null ? void 0 : options.auth) {\n    if (options.auth.type === \"Bearer\") {\n      const token = await getValue(options.auth.token);\n      if (!token) {\n        return headers;\n      }\n      headers[\"authorization\"] = `Bearer ${token}`;\n    } else if (options.auth.type === \"Basic\") {\n      const username = getValue(options.auth.username);\n      const password = getValue(options.auth.password);\n      if (!username || !password) {\n        return headers;\n      }\n      headers[\"authorization\"] = `Basic ${btoa(`${username}:${password}`)}`;\n    } else if (options.auth.type === \"Custom\") {\n      const value = getValue(options.auth.value);\n      if (!value) {\n        return headers;\n      }\n      headers[\"authorization\"] = `${getValue(options.auth.prefix)} ${value}`;\n    }\n  }\n  return headers;\n};\n\n// src/utils.ts\nvar JSON_RE = /^application\\/(?:[\\w!#$%&*.^`~-]*\\+)?json(;.+)?$/i;\nfunction detectResponseType(request) {\n  const _contentType = request.headers.get(\"content-type\");\n  const textTypes = /* @__PURE__ */ new Set([\n    \"image/svg\",\n    \"application/xml\",\n    \"application/xhtml\",\n    \"application/html\"\n  ]);\n  if (!_contentType) {\n    return \"json\";\n  }\n  const contentType = _contentType.split(\";\").shift() || \"\";\n  if (JSON_RE.test(contentType)) {\n    return \"json\";\n  }\n  if (textTypes.has(contentType) || contentType.startsWith(\"text/\")) {\n    return \"text\";\n  }\n  return \"blob\";\n}\nfunction isJSONParsable(value) {\n  try {\n    JSON.parse(value);\n    return true;\n  } catch (error) {\n    return false;\n  }\n}\nfunction isJSONSerializable(value) {\n  if (value === void 0) {\n    return false;\n  }\n  const t = typeof value;\n  if (t === \"string\" || t === \"number\" || t === \"boolean\" || t === null) {\n    return true;\n  }\n  if (t !== \"object\") {\n    return false;\n  }\n  if (Array.isArray(value)) {\n    return true;\n  }\n  if (value.buffer) {\n    return false;\n  }\n  return value.constructor && value.constructor.name === \"Object\" || typeof value.toJSON === \"function\";\n}\nfunction jsonParse(text) {\n  try {\n    return JSON.parse(text);\n  } catch (error) {\n    return text;\n  }\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\nfunction getFetch(options) {\n  if (options == null ? void 0 : options.customFetchImpl) {\n    return options.customFetchImpl;\n  }\n  if (typeof globalThis !== \"undefined\" && isFunction(globalThis.fetch)) {\n    return globalThis.fetch;\n  }\n  if (typeof window !== \"undefined\" && isFunction(window.fetch)) {\n    return window.fetch;\n  }\n  throw new Error(\"No fetch implementation found\");\n}\nfunction isPayloadMethod(method) {\n  if (!method) {\n    return false;\n  }\n  const payloadMethod = [\"POST\", \"PUT\", \"PATCH\", \"DELETE\"];\n  return payloadMethod.includes(method.toUpperCase());\n}\nfunction isRouteMethod(method) {\n  const routeMethod = [\"GET\", \"POST\", \"PUT\", \"PATCH\", \"DELETE\"];\n  if (!method) {\n    return false;\n  }\n  return routeMethod.includes(method.toUpperCase());\n}\nasync function getHeaders(opts) {\n  const headers = new Headers(opts == null ? void 0 : opts.headers);\n  const authHeader = await getAuthHeader(opts);\n  for (const [key, value] of Object.entries(authHeader || {})) {\n    headers.set(key, value);\n  }\n  if (!headers.has(\"content-type\")) {\n    const t = detectContentType(opts == null ? void 0 : opts.body);\n    if (t) {\n      headers.set(\"content-type\", t);\n    }\n  }\n  return headers;\n}\nfunction getURL(url, options) {\n  if (url.startsWith(\"@\")) {\n    const m = url.toString().split(\"@\")[1].split(\"/\")[0];\n    if (methods.includes(m)) {\n      url = url.replace(`@${m}/`, \"/\");\n    }\n  }\n  let _url;\n  try {\n    if (url.startsWith(\"http\")) {\n      _url = url;\n    } else {\n      let baseURL = options == null ? void 0 : options.baseURL;\n      if (baseURL && !(baseURL == null ? void 0 : baseURL.endsWith(\"/\"))) {\n        baseURL = baseURL + \"/\";\n      }\n      if (url.startsWith(\"/\")) {\n        _url = new URL(url.substring(1), baseURL);\n      } else {\n        _url = new URL(url, options == null ? void 0 : options.baseURL);\n      }\n    }\n  } catch (e) {\n    if (e instanceof TypeError) {\n      if (!(options == null ? void 0 : options.baseURL)) {\n        throw TypeError(\n          `Invalid URL ${url}. Are you passing in a relative url but not setting the baseURL?`\n        );\n      }\n      throw TypeError(\n        `Invalid URL ${url}. Please validate that you are passing the correct input.`\n      );\n    }\n    throw e;\n  }\n  if (options == null ? void 0 : options.params) {\n    if (Array.isArray(options == null ? void 0 : options.params)) {\n      const params = (options == null ? void 0 : options.params) ? Array.isArray(options.params) ? `/${options.params.join(\"/\")}` : `/${Object.values(options.params).join(\"/\")}` : \"\";\n      _url = _url.toString().split(\"/:\")[0];\n      _url = `${_url.toString()}${params}`;\n    } else {\n      for (const [key, value] of Object.entries(options == null ? void 0 : options.params)) {\n        _url = _url.toString().replace(`:${key}`, String(value));\n      }\n    }\n  }\n  const __url = new URL(_url);\n  const queryParams = options == null ? void 0 : options.query;\n  if (queryParams) {\n    for (const [key, value] of Object.entries(queryParams)) {\n      __url.searchParams.append(key, String(value));\n    }\n  }\n  return __url;\n}\nfunction detectContentType(body) {\n  if (isJSONSerializable(body)) {\n    return \"application/json\";\n  }\n  return null;\n}\nfunction getBody(options) {\n  if (!(options == null ? void 0 : options.body)) {\n    return null;\n  }\n  const headers = new Headers(options == null ? void 0 : options.headers);\n  if (isJSONSerializable(options.body) && !headers.has(\"content-type\")) {\n    for (const [key, value] of Object.entries(options == null ? void 0 : options.body)) {\n      if (value instanceof Date) {\n        options.body[key] = value.toISOString();\n      }\n    }\n    return JSON.stringify(options.body);\n  }\n  return options.body;\n}\nfunction getMethod(url, options) {\n  var _a;\n  if (options == null ? void 0 : options.method) {\n    return options.method.toUpperCase();\n  }\n  if (url.startsWith(\"@\")) {\n    const pMethod = (_a = url.split(\"@\")[1]) == null ? void 0 : _a.split(\"/\")[0];\n    if (!methods.includes(pMethod)) {\n      return (options == null ? void 0 : options.body) ? \"POST\" : \"GET\";\n    }\n    return pMethod.toUpperCase();\n  }\n  return (options == null ? void 0 : options.body) ? \"POST\" : \"GET\";\n}\nfunction getTimeout(options, controller) {\n  let abortTimeout;\n  if (!(options == null ? void 0 : options.signal) && (options == null ? void 0 : options.timeout)) {\n    abortTimeout = setTimeout(() => controller == null ? void 0 : controller.abort(), options == null ? void 0 : options.timeout);\n  }\n  return {\n    abortTimeout,\n    clearTimeout: () => {\n      if (abortTimeout) {\n        clearTimeout(abortTimeout);\n      }\n    }\n  };\n}\nfunction bodyParser(data, responseType) {\n  if (responseType === \"json\") {\n    return JSON.parse(data);\n  }\n  return data;\n}\nvar ValidationError = class _ValidationError extends Error {\n  constructor(issues, message) {\n    super(message || JSON.stringify(issues, null, 2));\n    this.issues = issues;\n    Object.setPrototypeOf(this, _ValidationError.prototype);\n  }\n};\nasync function parseStandardSchema(schema, input) {\n  let result = await schema[\"~standard\"].validate(input);\n  if (result.issues) {\n    throw new ValidationError(result.issues);\n  }\n  return result.value;\n}\n\n// src/create-fetch/schema.ts\nvar methods = [\"get\", \"post\", \"put\", \"patch\", \"delete\"];\nvar createSchema = (schema, config) => {\n  return {\n    schema,\n    config\n  };\n};\n\n// src/create-fetch/index.ts\nvar applySchemaPlugin = (config) => ({\n  id: \"apply-schema\",\n  name: \"Apply Schema\",\n  version: \"1.0.0\",\n  async init(url, options) {\n    var _a, _b, _c, _d;\n    const schema = ((_b = (_a = config.plugins) == null ? void 0 : _a.find(\n      (plugin) => {\n        var _a2;\n        return ((_a2 = plugin.schema) == null ? void 0 : _a2.config) ? url.startsWith(plugin.schema.config.baseURL || \"\") || url.startsWith(plugin.schema.config.prefix || \"\") : false;\n      }\n    )) == null ? void 0 : _b.schema) || config.schema;\n    if (schema) {\n      let urlKey = url;\n      if ((_c = schema.config) == null ? void 0 : _c.prefix) {\n        if (urlKey.startsWith(schema.config.prefix)) {\n          urlKey = urlKey.replace(schema.config.prefix, \"\");\n          if (schema.config.baseURL) {\n            url = url.replace(schema.config.prefix, schema.config.baseURL);\n          }\n        }\n      }\n      if ((_d = schema.config) == null ? void 0 : _d.baseURL) {\n        if (urlKey.startsWith(schema.config.baseURL)) {\n          urlKey = urlKey.replace(schema.config.baseURL, \"\");\n        }\n      }\n      const keySchema = schema.schema[urlKey];\n      if (keySchema) {\n        let opts = __spreadProps(__spreadValues({}, options), {\n          method: keySchema.method,\n          output: keySchema.output\n        });\n        if (!(options == null ? void 0 : options.disableValidation)) {\n          opts = __spreadProps(__spreadValues({}, opts), {\n            body: keySchema.input ? await parseStandardSchema(keySchema.input, options == null ? void 0 : options.body) : options == null ? void 0 : options.body,\n            params: keySchema.params ? await parseStandardSchema(keySchema.params, options == null ? void 0 : options.params) : options == null ? void 0 : options.params,\n            query: keySchema.query ? await parseStandardSchema(keySchema.query, options == null ? void 0 : options.query) : options == null ? void 0 : options.query\n          });\n        }\n        return {\n          url,\n          options: opts\n        };\n      }\n    }\n    return {\n      url,\n      options\n    };\n  }\n});\nvar createFetch = (config) => {\n  async function $fetch(url, options) {\n    const opts = __spreadProps(__spreadValues(__spreadValues({}, config), options), {\n      plugins: [...(config == null ? void 0 : config.plugins) || [], applySchemaPlugin(config || {})]\n    });\n    if (config == null ? void 0 : config.catchAllError) {\n      try {\n        return await betterFetch(url, opts);\n      } catch (error) {\n        return {\n          data: null,\n          error: {\n            status: 500,\n            statusText: \"Fetch Error\",\n            message: \"Fetch related error. Captured by catchAllError option. See error property for more details.\",\n            error\n          }\n        };\n      }\n    }\n    return await betterFetch(url, opts);\n  }\n  return $fetch;\n};\n\n// src/url.ts\nfunction getURL2(url, option) {\n  let { baseURL, params, query } = option || {\n    query: {},\n    params: {},\n    baseURL: \"\"\n  };\n  let basePath = url.startsWith(\"http\") ? url.split(\"/\").slice(0, 3).join(\"/\") : baseURL || \"\";\n  if (url.startsWith(\"@\")) {\n    const m = url.toString().split(\"@\")[1].split(\"/\")[0];\n    if (methods.includes(m)) {\n      url = url.replace(`@${m}/`, \"/\");\n    }\n  }\n  if (!basePath.endsWith(\"/\")) basePath += \"/\";\n  let [path, urlQuery] = url.replace(basePath, \"\").split(\"?\");\n  const queryParams = new URLSearchParams(urlQuery);\n  for (const [key, value] of Object.entries(query || {})) {\n    if (value == null) continue;\n    queryParams.set(key, String(value));\n  }\n  if (params) {\n    if (Array.isArray(params)) {\n      const paramPaths = path.split(\"/\").filter((p) => p.startsWith(\":\"));\n      for (const [index, key] of paramPaths.entries()) {\n        const value = params[index];\n        path = path.replace(key, value);\n      }\n    } else {\n      for (const [key, value] of Object.entries(params)) {\n        path = path.replace(`:${key}`, String(value));\n      }\n    }\n  }\n  path = path.split(\"/\").map(encodeURIComponent).join(\"/\");\n  if (path.startsWith(\"/\")) path = path.slice(1);\n  let queryParamString = queryParams.toString();\n  queryParamString = queryParamString.length > 0 ? `?${queryParamString}`.replace(/\\+/g, \"%20\") : \"\";\n  if (!basePath.startsWith(\"http\")) {\n    return `${basePath}${path}${queryParamString}`;\n  }\n  const _url = new URL(`${path}${queryParamString}`, basePath);\n  return _url;\n}\n\n// src/fetch.ts\nvar betterFetch = async (url, options) => {\n  var _a, _b, _c, _d, _e, _f, _g, _h;\n  const {\n    hooks,\n    url: __url,\n    options: opts\n  } = await initializePlugins(url, options);\n  const fetch = getFetch(opts);\n  const controller = new AbortController();\n  const signal = (_a = opts.signal) != null ? _a : controller.signal;\n  const _url = getURL2(__url, opts);\n  const body = getBody(opts);\n  const headers = await getHeaders(opts);\n  const method = getMethod(__url, opts);\n  let context = __spreadProps(__spreadValues({}, opts), {\n    url: _url,\n    headers,\n    body,\n    method,\n    signal\n  });\n  for (const onRequest of hooks.onRequest) {\n    if (onRequest) {\n      const res = await onRequest(context);\n      if (res instanceof Object) {\n        context = res;\n      }\n    }\n  }\n  if (\"pipeTo\" in context && typeof context.pipeTo === \"function\" || typeof ((_b = options == null ? void 0 : options.body) == null ? void 0 : _b.pipe) === \"function\") {\n    if (!(\"duplex\" in context)) {\n      context.duplex = \"half\";\n    }\n  }\n  const { clearTimeout: clearTimeout2 } = getTimeout(opts, controller);\n  let response = await fetch(context.url, context);\n  clearTimeout2();\n  const responseContext = {\n    response,\n    request: context\n  };\n  for (const onResponse of hooks.onResponse) {\n    if (onResponse) {\n      const r = await onResponse(__spreadProps(__spreadValues({}, responseContext), {\n        response: ((_c = options == null ? void 0 : options.hookOptions) == null ? void 0 : _c.cloneResponse) ? response.clone() : response\n      }));\n      if (r instanceof Response) {\n        response = r;\n      } else if (r instanceof Object) {\n        response = r.response;\n      }\n    }\n  }\n  if (response.ok) {\n    const hasBody = context.method !== \"HEAD\";\n    if (!hasBody) {\n      return {\n        data: \"\",\n        error: null\n      };\n    }\n    const responseType = detectResponseType(response);\n    const successContext = {\n      data: \"\",\n      response,\n      request: context\n    };\n    if (responseType === \"json\" || responseType === \"text\") {\n      const text = await response.text();\n      const parser2 = (_d = context.jsonParser) != null ? _d : jsonParse;\n      const data = await parser2(text);\n      successContext.data = data;\n    } else {\n      successContext.data = await response[responseType]();\n    }\n    if (context == null ? void 0 : context.output) {\n      if (context.output && !context.disableValidation) {\n        successContext.data = await parseStandardSchema(\n          context.output,\n          successContext.data\n        );\n      }\n    }\n    for (const onSuccess of hooks.onSuccess) {\n      if (onSuccess) {\n        await onSuccess(__spreadProps(__spreadValues({}, successContext), {\n          response: ((_e = options == null ? void 0 : options.hookOptions) == null ? void 0 : _e.cloneResponse) ? response.clone() : response\n        }));\n      }\n    }\n    if (options == null ? void 0 : options.throw) {\n      return successContext.data;\n    }\n    return {\n      data: successContext.data,\n      error: null\n    };\n  }\n  const parser = (_f = options == null ? void 0 : options.jsonParser) != null ? _f : jsonParse;\n  const responseText = await response.text();\n  const isJSONResponse = isJSONParsable(responseText);\n  const errorObject = isJSONResponse ? await parser(responseText) : null;\n  const errorContext = {\n    response,\n    responseText,\n    request: context,\n    error: __spreadProps(__spreadValues({}, errorObject), {\n      status: response.status,\n      statusText: response.statusText\n    })\n  };\n  for (const onError of hooks.onError) {\n    if (onError) {\n      await onError(__spreadProps(__spreadValues({}, errorContext), {\n        response: ((_g = options == null ? void 0 : options.hookOptions) == null ? void 0 : _g.cloneResponse) ? response.clone() : response\n      }));\n    }\n  }\n  if (options == null ? void 0 : options.retry) {\n    const retryStrategy = createRetryStrategy(options.retry);\n    const _retryAttempt = (_h = options.retryAttempt) != null ? _h : 0;\n    if (await retryStrategy.shouldAttemptRetry(_retryAttempt, response)) {\n      for (const onRetry of hooks.onRetry) {\n        if (onRetry) {\n          await onRetry(responseContext);\n        }\n      }\n      const delay = retryStrategy.getDelay(_retryAttempt);\n      await new Promise((resolve) => setTimeout(resolve, delay));\n      return await betterFetch(url, __spreadProps(__spreadValues({}, options), {\n        retryAttempt: _retryAttempt + 1\n      }));\n    }\n  }\n  if (options == null ? void 0 : options.throw) {\n    throw new BetterFetchError(\n      response.status,\n      response.statusText,\n      isJSONResponse ? errorObject : responseText\n    );\n  }\n  return {\n    data: null,\n    error: __spreadProps(__spreadValues({}, errorObject), {\n      status: response.status,\n      statusText: response.statusText\n    })\n  };\n};\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@better-fetch+fetch@1.1.18/node_modules/@better-fetch/fetch/dist/index.js\n");

/***/ })

};
;