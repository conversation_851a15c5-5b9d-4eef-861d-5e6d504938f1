"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/better-auth@1.2.7";
exports.ids = ["vendor-chunks/better-auth@1.2.7"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/client/react/index.mjs":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/client/react/index.mjs ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirstLetter: () => (/* binding */ capitalizeFirstLetter),\n/* harmony export */   createAuthClient: () => (/* binding */ createAuthClient),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var _shared_better_auth_DYm_YVE1_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/better-auth.DYm-YVE1.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.DYm-YVE1.mjs\");\n/* harmony import */ var nanostores__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! nanostores */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/listen-keys/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _better_fetch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @better-fetch/fetch */ \"(ssr)/../../node_modules/.pnpm/@better-fetch+fetch@1.1.18/node_modules/@better-fetch/fetch/dist/index.js\");\n/* harmony import */ var _shared_better_auth_VTXNLFMT_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/better-auth.VTXNLFMT.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs\");\n/* harmony import */ var _shared_better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../shared/better-auth.8zoxzg-F.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs\");\n/* harmony import */ var _shared_better_auth_DdzSJf_n_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/better-auth.DdzSJf-n.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs\");\n/* harmony import */ var _shared_better_auth_CQvoVIBD_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../shared/better-auth.CQvoVIBD.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.CQvoVIBD.mjs\");\n/* harmony import */ var _shared_better_auth_ffWeg50w_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../shared/better-auth.ffWeg50w.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs\");\n\n\n\n\n\n\n\n\n\n\nfunction useStore(store, options = {}) {\n  let snapshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(store.get());\n  const { keys, deps = [store, keys] } = options;\n  let subscribe = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((onChange) => {\n    const emitChange = (value) => {\n      if (snapshotRef.current === value) return;\n      snapshotRef.current = value;\n      onChange();\n    };\n    emitChange(store.value);\n    if (keys?.length) {\n      return (0,nanostores__WEBPACK_IMPORTED_MODULE_8__.listenKeys)(store, keys, emitChange);\n    }\n    return store.listen(emitChange);\n  }, deps);\n  let get = () => snapshotRef.current;\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)(subscribe, get, get);\n}\n\nfunction getAtomKey(str) {\n  return `use${capitalizeFirstLetter(str)}`;\n}\nfunction capitalizeFirstLetter(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nfunction createAuthClient(options) {\n  const {\n    pluginPathMethods,\n    pluginsActions,\n    pluginsAtoms,\n    $fetch,\n    $store,\n    atomListeners\n  } = (0,_shared_better_auth_DYm_YVE1_mjs__WEBPACK_IMPORTED_MODULE_0__.g)(options);\n  let resolvedHooks = {};\n  for (const [key, value] of Object.entries(pluginsAtoms)) {\n    resolvedHooks[getAtomKey(key)] = () => useStore(value);\n  }\n  const routes = {\n    ...pluginsActions,\n    ...resolvedHooks,\n    $fetch,\n    $store\n  };\n  const proxy = (0,_shared_better_auth_DYm_YVE1_mjs__WEBPACK_IMPORTED_MODULE_0__.c)(\n    routes,\n    $fetch,\n    pluginPathMethods,\n    pluginsAtoms,\n    atomListeners\n  );\n  return proxy;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/client/react/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs":
/*!****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ isProduction),\n/* harmony export */   b: () => (/* binding */ isDevelopment),\n/* harmony export */   e: () => (/* binding */ env),\n/* harmony export */   i: () => (/* binding */ isTest)\n/* harmony export */ });\nconst _envShim = /* @__PURE__ */ Object.create(null);\nconst _getEnv = (useShim) => globalThis.process?.env || //@ts-expect-error\nglobalThis.Deno?.env.toObject() || //@ts-expect-error\nglobalThis.__env__ || (useShim ? _envShim : globalThis);\nconst env = new Proxy(_envShim, {\n  get(_, prop) {\n    const env2 = _getEnv();\n    return env2[prop] ?? _envShim[prop];\n  },\n  has(_, prop) {\n    const env2 = _getEnv();\n    return prop in env2 || prop in _envShim;\n  },\n  set(_, prop, value) {\n    const env2 = _getEnv(true);\n    env2[prop] = value;\n    return true;\n  },\n  deleteProperty(_, prop) {\n    if (!prop) {\n      return false;\n    }\n    const env2 = _getEnv(true);\n    delete env2[prop];\n    return true;\n  },\n  ownKeys() {\n    const env2 = _getEnv(true);\n    return Object.keys(env2);\n  }\n});\nfunction toBoolean(val) {\n  return val ? val !== \"false\" : false;\n}\nconst nodeENV = typeof process !== \"undefined\" && process.env && \"development\" || \"\";\nconst isProduction = nodeENV === \"production\";\nconst isDevelopment = nodeENV === \"dev\" || nodeENV === \"development\";\nconst isTest = nodeENV === \"test\" || toBoolean(env.TEST);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.CQvoVIBD.mjs":
/*!****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.CQvoVIBD.mjs ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   u: () => (/* binding */ useAuthQuery)\n/* harmony export */ });\n/* harmony import */ var nanostores__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanostores */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/atom/index.js\");\n/* harmony import */ var nanostores__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! nanostores */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/lifecycle/index.js\");\n\n\nconst useAuthQuery = (initializedAtom, path, $fetch, options) => {\n  const value = (0,nanostores__WEBPACK_IMPORTED_MODULE_0__.atom)({\n    data: null,\n    error: null,\n    isPending: true,\n    isRefetching: false,\n    refetch: () => {\n      return fn();\n    }\n  });\n  const fn = () => {\n    const opts = typeof options === \"function\" ? options({\n      data: value.get().data,\n      error: value.get().error,\n      isPending: value.get().isPending\n    }) : options;\n    return $fetch(path, {\n      ...opts,\n      async onSuccess(context) {\n        if (typeof window !== \"undefined\") {\n          value.set({\n            data: context.data,\n            error: null,\n            isPending: false,\n            isRefetching: false,\n            refetch: value.value.refetch\n          });\n        }\n        await opts?.onSuccess?.(context);\n      },\n      async onError(context) {\n        const { request } = context;\n        const retryAttempts = typeof request.retry === \"number\" ? request.retry : request.retry?.attempts;\n        const retryAttempt = request.retryAttempt || 0;\n        if (retryAttempts && retryAttempt < retryAttempts) return;\n        value.set({\n          error: context.error,\n          data: null,\n          isPending: false,\n          isRefetching: false,\n          refetch: value.value.refetch\n        });\n        await opts?.onError?.(context);\n      },\n      async onRequest(context) {\n        const currentValue = value.get();\n        value.set({\n          isPending: currentValue.data === null,\n          data: currentValue.data,\n          error: null,\n          isRefetching: true,\n          refetch: value.value.refetch\n        });\n        await opts?.onRequest?.(context);\n      }\n    });\n  };\n  initializedAtom = Array.isArray(initializedAtom) ? initializedAtom : [initializedAtom];\n  let isMounted = false;\n  for (const initAtom of initializedAtom) {\n    initAtom.subscribe(() => {\n      if (isMounted) {\n        fn();\n      } else {\n        (0,nanostores__WEBPACK_IMPORTED_MODULE_1__.onMount)(value, () => {\n          fn();\n          isMounted = true;\n          return () => {\n            value.off();\n            initAtom.off();\n          };\n        });\n      }\n    });\n  }\n  return value;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.CQvoVIBD.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.DYm-YVE1.mjs":
/*!****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.DYm-YVE1.mjs ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   c: () => (/* binding */ createDynamicPathProxy),\n/* harmony export */   g: () => (/* binding */ getClientConfig)\n/* harmony export */ });\n/* harmony import */ var _better_fetch_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @better-fetch/fetch */ \"(ssr)/../../node_modules/.pnpm/@better-fetch+fetch@1.1.18/node_modules/@better-fetch/fetch/dist/index.js\");\n/* harmony import */ var _better_auth_VTXNLFMT_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./better-auth.VTXNLFMT.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs\");\n/* harmony import */ var nanostores__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! nanostores */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/atom/index.js\");\n/* harmony import */ var _better_auth_CQvoVIBD_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./better-auth.CQvoVIBD.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.CQvoVIBD.mjs\");\n/* harmony import */ var _better_auth_ffWeg50w_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./better-auth.ffWeg50w.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs\");\n\n\n\n\n\n\nconst redirectPlugin = {\n  id: \"redirect\",\n  name: \"Redirect\",\n  hooks: {\n    onSuccess(context) {\n      if (context.data?.url && context.data?.redirect) {\n        if (typeof window !== \"undefined\" && window.location) {\n          if (window.location) {\n            try {\n              window.location.href = context.data.url;\n            } catch {\n            }\n          }\n        }\n      }\n    }\n  }\n};\n\nfunction getSessionAtom($fetch) {\n  const $signal = (0,nanostores__WEBPACK_IMPORTED_MODULE_4__.atom)(false);\n  const session = (0,_better_auth_CQvoVIBD_mjs__WEBPACK_IMPORTED_MODULE_2__.u)($signal, \"/get-session\", $fetch, {\n    method: \"GET\"\n  });\n  return {\n    session,\n    $sessionSignal: $signal\n  };\n}\n\nconst getClientConfig = (options) => {\n  const isCredentialsSupported = \"credentials\" in Request.prototype;\n  const baseURL = (0,_better_auth_VTXNLFMT_mjs__WEBPACK_IMPORTED_MODULE_1__.a)(options?.baseURL, options?.basePath);\n  const pluginsFetchPlugins = options?.plugins?.flatMap((plugin) => plugin.fetchPlugins).filter((pl) => pl !== void 0) || [];\n  const $fetch = (0,_better_fetch_fetch__WEBPACK_IMPORTED_MODULE_0__.createFetch)({\n    baseURL,\n    ...isCredentialsSupported ? { credentials: \"include\" } : {},\n    method: \"GET\",\n    jsonParser(text) {\n      if (!text) {\n        return null;\n      }\n      return (0,_better_auth_ffWeg50w_mjs__WEBPACK_IMPORTED_MODULE_3__.p)(text, {\n        strict: false\n      });\n    },\n    customFetchImpl: async (input, init) => {\n      try {\n        return await fetch(input, init);\n      } catch (error) {\n        return Response.error();\n      }\n    },\n    ...options?.fetchOptions,\n    plugins: options?.disableDefaultFetchPlugins ? [...options?.fetchOptions?.plugins || [], ...pluginsFetchPlugins] : [\n      redirectPlugin,\n      ...options?.fetchOptions?.plugins || [],\n      ...pluginsFetchPlugins\n    ]\n  });\n  const { $sessionSignal, session } = getSessionAtom($fetch);\n  const plugins = options?.plugins || [];\n  let pluginsActions = {};\n  let pluginsAtoms = {\n    $sessionSignal,\n    session\n  };\n  let pluginPathMethods = {\n    \"/sign-out\": \"POST\",\n    \"/revoke-sessions\": \"POST\",\n    \"/revoke-other-sessions\": \"POST\",\n    \"/delete-user\": \"POST\"\n  };\n  const atomListeners = [\n    {\n      signal: \"$sessionSignal\",\n      matcher(path) {\n        return path === \"/sign-out\" || path === \"/update-user\" || path.startsWith(\"/sign-in\") || path.startsWith(\"/sign-up\") || path === \"/delete-user\" || path === \"/verify-email\";\n      }\n    }\n  ];\n  for (const plugin of plugins) {\n    if (plugin.getAtoms) {\n      Object.assign(pluginsAtoms, plugin.getAtoms?.($fetch));\n    }\n    if (plugin.pathMethods) {\n      Object.assign(pluginPathMethods, plugin.pathMethods);\n    }\n    if (plugin.atomListeners) {\n      atomListeners.push(...plugin.atomListeners);\n    }\n  }\n  const $store = {\n    notify: (signal) => {\n      pluginsAtoms[signal].set(\n        !pluginsAtoms[signal].get()\n      );\n    },\n    listen: (signal, listener) => {\n      pluginsAtoms[signal].subscribe(listener);\n    },\n    atoms: pluginsAtoms\n  };\n  for (const plugin of plugins) {\n    if (plugin.getActions) {\n      Object.assign(pluginsActions, plugin.getActions?.($fetch, $store));\n    }\n  }\n  return {\n    pluginsActions,\n    pluginsAtoms,\n    pluginPathMethods,\n    atomListeners,\n    $fetch,\n    $store\n  };\n};\n\nfunction getMethod(path, knownPathMethods, args) {\n  const method = knownPathMethods[path];\n  const { fetchOptions, query, ...body } = args || {};\n  if (method) {\n    return method;\n  }\n  if (fetchOptions?.method) {\n    return fetchOptions.method;\n  }\n  if (body && Object.keys(body).length > 0) {\n    return \"POST\";\n  }\n  return \"GET\";\n}\nfunction createDynamicPathProxy(routes, client, knownPathMethods, atoms, atomListeners) {\n  function createProxy(path = []) {\n    return new Proxy(function() {\n    }, {\n      get(target, prop) {\n        const fullPath = [...path, prop];\n        let current = routes;\n        for (const segment of fullPath) {\n          if (current && typeof current === \"object\" && segment in current) {\n            current = current[segment];\n          } else {\n            current = void 0;\n            break;\n          }\n        }\n        if (typeof current === \"function\") {\n          return current;\n        }\n        return createProxy(fullPath);\n      },\n      apply: async (_, __, args) => {\n        const routePath = \"/\" + path.map(\n          (segment) => segment.replace(/[A-Z]/g, (letter) => `-${letter.toLowerCase()}`)\n        ).join(\"/\");\n        const arg = args[0] || {};\n        const fetchOptions = args[1] || {};\n        const { query, fetchOptions: argFetchOptions, ...body } = arg;\n        const options = {\n          ...fetchOptions,\n          ...argFetchOptions\n        };\n        const method = getMethod(routePath, knownPathMethods, arg);\n        return await client(routePath, {\n          ...options,\n          body: method === \"GET\" ? void 0 : {\n            ...body,\n            ...options?.body || {}\n          },\n          query: query || options?.query,\n          method,\n          async onSuccess(context) {\n            await options?.onSuccess?.(context);\n            const matches = atomListeners?.find((s) => s.matcher(routePath));\n            if (!matches) return;\n            const signal = atoms[matches.signal];\n            if (!signal) return;\n            const val = signal.get();\n            setTimeout(() => {\n              signal.set(!val);\n            }, 10);\n          }\n        });\n      }\n    });\n  }\n  return createProxy();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.DYm-YVE1.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs":
/*!****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   B: () => (/* binding */ BetterAuthError),\n/* harmony export */   M: () => (/* binding */ MissingDependencyError)\n/* harmony export */ });\nclass BetterAuthError extends Error {\n  constructor(message, cause) {\n    super(message);\n    this.name = \"BetterAuthError\";\n    this.message = message;\n    this.cause = cause;\n    this.stack = \"\";\n  }\n}\nclass MissingDependencyError extends BetterAuthError {\n  constructor(pkgName) {\n    super(\n      `The package \"${pkgName}\" is required. Make sure it is installed.`,\n      pkgName\n    );\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2JldHRlci1hdXRoQDEuMi43L25vZGVfbW9kdWxlcy9iZXR0ZXItYXV0aC9kaXN0L3NoYXJlZC9iZXR0ZXItYXV0aC5EZHpTSmYtbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsUUFBUTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTs7QUFFNkQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUiAxXFxEZXNrdG9wXFxkZXZvdHNwLXYxLjVcXHBvcnRhbHdpemFyZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcYmV0dGVyLWF1dGhAMS4yLjdcXG5vZGVfbW9kdWxlc1xcYmV0dGVyLWF1dGhcXGRpc3RcXHNoYXJlZFxcYmV0dGVyLWF1dGguRGR6U0pmLW4ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNsYXNzIEJldHRlckF1dGhFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgY29uc3RydWN0b3IobWVzc2FnZSwgY2F1c2UpIHtcbiAgICBzdXBlcihtZXNzYWdlKTtcbiAgICB0aGlzLm5hbWUgPSBcIkJldHRlckF1dGhFcnJvclwiO1xuICAgIHRoaXMubWVzc2FnZSA9IG1lc3NhZ2U7XG4gICAgdGhpcy5jYXVzZSA9IGNhdXNlO1xuICAgIHRoaXMuc3RhY2sgPSBcIlwiO1xuICB9XG59XG5jbGFzcyBNaXNzaW5nRGVwZW5kZW5jeUVycm9yIGV4dGVuZHMgQmV0dGVyQXV0aEVycm9yIHtcbiAgY29uc3RydWN0b3IocGtnTmFtZSkge1xuICAgIHN1cGVyKFxuICAgICAgYFRoZSBwYWNrYWdlIFwiJHtwa2dOYW1lfVwiIGlzIHJlcXVpcmVkLiBNYWtlIHN1cmUgaXQgaXMgaW5zdGFsbGVkLmAsXG4gICAgICBwa2dOYW1lXG4gICAgKTtcbiAgfVxufVxuXG5leHBvcnQgeyBCZXR0ZXJBdXRoRXJyb3IgYXMgQiwgTWlzc2luZ0RlcGVuZGVuY3lFcnJvciBhcyBNIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs":
/*!****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ getBaseURL),\n/* harmony export */   b: () => (/* binding */ getHost),\n/* harmony export */   c: () => (/* binding */ getProtocol),\n/* harmony export */   g: () => (/* binding */ getOrigin)\n/* harmony export */ });\n/* harmony import */ var _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./better-auth.8zoxzg-F.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs\");\n/* harmony import */ var _better_auth_DdzSJf_n_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./better-auth.DdzSJf-n.mjs */ \"(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs\");\n\n\n\nfunction checkHasPath(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.pathname !== \"/\";\n  } catch (error) {\n    throw new _better_auth_DdzSJf_n_mjs__WEBPACK_IMPORTED_MODULE_1__.B(\n      `Invalid base URL: ${url}. Please provide a valid base URL.`\n    );\n  }\n}\nfunction withPath(url, path = \"/api/auth\") {\n  const hasPath = checkHasPath(url);\n  if (hasPath) {\n    return url;\n  }\n  path = path.startsWith(\"/\") ? path : `/${path}`;\n  return `${url.replace(/\\/+$/, \"\")}${path}`;\n}\nfunction getBaseURL(url, path, request) {\n  if (url) {\n    return withPath(url, path);\n  }\n  const fromEnv = _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.BETTER_AUTH_URL || _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.NEXT_PUBLIC_BETTER_AUTH_URL || _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.PUBLIC_BETTER_AUTH_URL || _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.NUXT_PUBLIC_BETTER_AUTH_URL || _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.NUXT_PUBLIC_AUTH_URL || (_better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.BASE_URL !== \"/\" ? _better_auth_8zoxzg_F_mjs__WEBPACK_IMPORTED_MODULE_0__.e.BASE_URL : void 0);\n  if (fromEnv) {\n    return withPath(fromEnv, path);\n  }\n  const fromRequest = request?.headers.get(\"x-forwarded-host\");\n  const fromRequestProto = request?.headers.get(\"x-forwarded-proto\");\n  if (fromRequest && fromRequestProto) {\n    return withPath(`${fromRequestProto}://${fromRequest}`, path);\n  }\n  if (request) {\n    const url2 = getOrigin(request.url);\n    if (!url2) {\n      throw new _better_auth_DdzSJf_n_mjs__WEBPACK_IMPORTED_MODULE_1__.B(\n        \"Could not get origin from request. Please provide a valid base URL.\"\n      );\n    }\n    return withPath(url2, path);\n  }\n  if (typeof window !== \"undefined\" && window.location) {\n    return withPath(window.location.origin, path);\n  }\n  return void 0;\n}\nfunction getOrigin(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.origin;\n  } catch (error) {\n    return null;\n  }\n}\nfunction getProtocol(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.protocol;\n  } catch (error) {\n    return null;\n  }\n}\nfunction getHost(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.host;\n  } catch (error) {\n    return url;\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs":
/*!****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   p: () => (/* binding */ parseJSON)\n/* harmony export */ });\nconst PROTO_POLLUTION_PATTERNS = {\n  proto: /\"(?:_|\\\\u0{2}5[Ff]){2}(?:p|\\\\u0{2}70)(?:r|\\\\u0{2}72)(?:o|\\\\u0{2}6[Ff])(?:t|\\\\u0{2}74)(?:o|\\\\u0{2}6[Ff])(?:_|\\\\u0{2}5[Ff]){2}\"\\s*:/,\n  constructor: /\"(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)\"\\s*:/,\n  protoShort: /\"__proto__\"\\s*:/,\n  constructorShort: /\"constructor\"\\s*:/\n};\nconst JSON_SIGNATURE = /^\\s*[\"[{]|^\\s*-?\\d{1,16}(\\.\\d{1,17})?([Ee][+-]?\\d+)?\\s*$/;\nconst SPECIAL_VALUES = {\n  true: true,\n  false: false,\n  null: null,\n  undefined: void 0,\n  nan: Number.NaN,\n  infinity: Number.POSITIVE_INFINITY,\n  \"-infinity\": Number.NEGATIVE_INFINITY\n};\nconst ISO_DATE_REGEX = /^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d{1,7}))?(?:Z|([+-])(\\d{2}):(\\d{2}))$/;\nfunction isValidDate(date) {\n  return date instanceof Date && !isNaN(date.getTime());\n}\nfunction parseISODate(value) {\n  const match = ISO_DATE_REGEX.exec(value);\n  if (!match) return null;\n  const [\n    ,\n    year,\n    month,\n    day,\n    hour,\n    minute,\n    second,\n    ms,\n    offsetSign,\n    offsetHour,\n    offsetMinute\n  ] = match;\n  let date = new Date(\n    Date.UTC(\n      parseInt(year, 10),\n      parseInt(month, 10) - 1,\n      parseInt(day, 10),\n      parseInt(hour, 10),\n      parseInt(minute, 10),\n      parseInt(second, 10),\n      ms ? parseInt(ms.padEnd(3, \"0\"), 10) : 0\n    )\n  );\n  if (offsetSign) {\n    const offset = (parseInt(offsetHour, 10) * 60 + parseInt(offsetMinute, 10)) * (offsetSign === \"+\" ? -1 : 1);\n    date.setUTCMinutes(date.getUTCMinutes() + offset);\n  }\n  return isValidDate(date) ? date : null;\n}\nfunction betterJSONParse(value, options = {}) {\n  const {\n    strict = false,\n    warnings = false,\n    reviver,\n    parseDates = true\n  } = options;\n  if (typeof value !== \"string\") {\n    return value;\n  }\n  const trimmed = value.trim();\n  if (trimmed[0] === '\"' && trimmed.endsWith('\"') && !trimmed.slice(1, -1).includes('\"')) {\n    return trimmed.slice(1, -1);\n  }\n  const lowerValue = trimmed.toLowerCase();\n  if (lowerValue.length <= 9 && lowerValue in SPECIAL_VALUES) {\n    return SPECIAL_VALUES[lowerValue];\n  }\n  if (!JSON_SIGNATURE.test(trimmed)) {\n    if (strict) {\n      throw new SyntaxError(\"[better-json] Invalid JSON\");\n    }\n    return value;\n  }\n  const hasProtoPattern = Object.entries(PROTO_POLLUTION_PATTERNS).some(\n    ([key, pattern]) => {\n      const matches = pattern.test(trimmed);\n      if (matches && warnings) {\n        console.warn(\n          `[better-json] Detected potential prototype pollution attempt using ${key} pattern`\n        );\n      }\n      return matches;\n    }\n  );\n  if (hasProtoPattern && strict) {\n    throw new Error(\n      \"[better-json] Potential prototype pollution attempt detected\"\n    );\n  }\n  try {\n    const secureReviver = (key, value2) => {\n      if (key === \"__proto__\" || key === \"constructor\" && value2 && typeof value2 === \"object\" && \"prototype\" in value2) {\n        if (warnings) {\n          console.warn(\n            `[better-json] Dropping \"${key}\" key to prevent prototype pollution`\n          );\n        }\n        return void 0;\n      }\n      if (parseDates && typeof value2 === \"string\") {\n        const date = parseISODate(value2);\n        if (date) {\n          return date;\n        }\n      }\n      return reviver ? reviver(key, value2) : value2;\n    };\n    return JSON.parse(trimmed, secureReviver);\n  } catch (error) {\n    if (strict) {\n      throw error;\n    }\n    return value;\n  }\n}\nfunction parseJSON(value, options = { strict: true }) {\n  return betterJSONParse(value, options);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/better-auth@1.2.7/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs\n");

/***/ })

};
;