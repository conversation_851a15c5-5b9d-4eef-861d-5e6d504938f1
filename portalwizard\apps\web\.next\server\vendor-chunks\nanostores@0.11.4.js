"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nanostores@0.11.4";
exports.ids = ["vendor-chunks/nanostores@0.11.4"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/atom/index.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/atom/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   atom: () => (/* binding */ atom),\n/* harmony export */   epoch: () => (/* binding */ epoch)\n/* harmony export */ });\n/* harmony import */ var _clean_stores_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../clean-stores/index.js */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/clean-stores/index.js\");\n\n\nlet listenerQueue = []\nlet lqIndex = 0\nconst QUEUE_ITEMS_PER_LISTENER = 4\nlet epoch = 0\n\nlet atom = (initialValue) => {\n  let listeners = []\n  let $atom = {\n    get() {\n      if (!$atom.lc) {\n        $atom.listen(() => {})()\n      }\n      return $atom.value\n    },\n    lc: 0,\n    listen(listener) {\n      $atom.lc = listeners.push(listener)\n\n      return () => {\n        for (let i = lqIndex + QUEUE_ITEMS_PER_LISTENER; i < listenerQueue.length;) {\n          if (listenerQueue[i] === listener) {\n            listenerQueue.splice(i, QUEUE_ITEMS_PER_LISTENER)\n          } else {\n            i += QUEUE_ITEMS_PER_LISTENER\n          }\n        }\n\n        let index = listeners.indexOf(listener)\n        if (~index) {\n          listeners.splice(index, 1)\n          if (!--$atom.lc) $atom.off()\n        }\n      }\n    },\n    notify(oldValue, changedKey) {\n      epoch++\n      let runListenerQueue = !listenerQueue.length\n      for (let listener of listeners) {\n        listenerQueue.push(\n          listener,\n          $atom.value,\n          oldValue,\n          changedKey\n        )\n      }\n\n      if (runListenerQueue) {\n        for (lqIndex = 0; lqIndex < listenerQueue.length; lqIndex += QUEUE_ITEMS_PER_LISTENER) {\n            listenerQueue[lqIndex](\n              listenerQueue[lqIndex + 1],\n              listenerQueue[lqIndex + 2],\n              listenerQueue[lqIndex + 3]\n            )\n        }\n        listenerQueue.length = 0\n      }\n    },\n    /* It will be called on last listener unsubscribing.\n       We will redefine it in onMount and onStop. */\n    off() {},\n    set(newValue) {\n      let oldValue = $atom.value\n      if (oldValue !== newValue) {\n        $atom.value = newValue\n        $atom.notify(oldValue)\n      }\n    },\n    subscribe(listener) {\n      let unbind = $atom.listen(listener)\n      listener($atom.value)\n      return unbind\n    },\n    value: initialValue\n  }\n\n  if (true) {\n    $atom[_clean_stores_index_js__WEBPACK_IMPORTED_MODULE_0__.clean] = () => {\n      listeners = []\n      $atom.lc = 0\n      $atom.off()\n    }\n  }\n\n  return $atom\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/atom/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/clean-stores/index.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/clean-stores/index.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clean: () => (/* binding */ clean),\n/* harmony export */   cleanStores: () => (/* binding */ cleanStores)\n/* harmony export */ });\n/* harmony import */ var _task_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../task/index.js */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/task/index.js\");\n\n\nlet clean = Symbol('clean')\n\nlet cleanStores = (...stores) => {\n  if (false) {}\n  (0,_task_index_js__WEBPACK_IMPORTED_MODULE_0__.cleanTasks)()\n  for (let $store of stores) {\n    if ($store) {\n      if ($store.mocked) delete $store.mocked\n      if ($store[clean]) $store[clean]()\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25hbm9zdG9yZXNAMC4xMS40L25vZGVfbW9kdWxlcy9uYW5vc3RvcmVzL2NsZWFuLXN0b3Jlcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7O0FBRXRDOztBQUVBO0FBQ1AsTUFBTSxLQUFxQyxFQUFFLEVBSTFDO0FBQ0gsRUFBRSwwREFBVTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVIgMVxcRGVza3RvcFxcZGV2b3RzcC12MS41XFxwb3J0YWx3aXphcmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG5hbm9zdG9yZXNAMC4xMS40XFxub2RlX21vZHVsZXNcXG5hbm9zdG9yZXNcXGNsZWFuLXN0b3Jlc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xlYW5UYXNrcyB9IGZyb20gJy4uL3Rhc2svaW5kZXguanMnXG5cbmV4cG9ydCBsZXQgY2xlYW4gPSBTeW1ib2woJ2NsZWFuJylcblxuZXhwb3J0IGxldCBjbGVhblN0b3JlcyA9ICguLi5zdG9yZXMpID0+IHtcbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnY2xlYW5TdG9yZXMoKSBjYW4gYmUgdXNlZCBvbmx5IGR1cmluZyBkZXZlbG9wbWVudCBvciB0ZXN0cydcbiAgICApXG4gIH1cbiAgY2xlYW5UYXNrcygpXG4gIGZvciAobGV0ICRzdG9yZSBvZiBzdG9yZXMpIHtcbiAgICBpZiAoJHN0b3JlKSB7XG4gICAgICBpZiAoJHN0b3JlLm1vY2tlZCkgZGVsZXRlICRzdG9yZS5tb2NrZWRcbiAgICAgIGlmICgkc3RvcmVbY2xlYW5dKSAkc3RvcmVbY2xlYW5dKClcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/clean-stores/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/lifecycle/index.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/lifecycle/index.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORE_UNMOUNT_DELAY: () => (/* binding */ STORE_UNMOUNT_DELAY),\n/* harmony export */   on: () => (/* binding */ on),\n/* harmony export */   onMount: () => (/* binding */ onMount),\n/* harmony export */   onNotify: () => (/* binding */ onNotify),\n/* harmony export */   onSet: () => (/* binding */ onSet),\n/* harmony export */   onStart: () => (/* binding */ onStart),\n/* harmony export */   onStop: () => (/* binding */ onStop)\n/* harmony export */ });\n/* harmony import */ var _clean_stores_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../clean-stores/index.js */ \"(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/clean-stores/index.js\");\n\n\nconst START = 0\nconst STOP = 1\nconst SET = 2\nconst NOTIFY = 3\nconst MOUNT = 5\nconst UNMOUNT = 6\nconst REVERT_MUTATION = 10\n\nlet on = (object, listener, eventKey, mutateStore) => {\n  object.events = object.events || {}\n  if (!object.events[eventKey + REVERT_MUTATION]) {\n    object.events[eventKey + REVERT_MUTATION] = mutateStore(eventProps => {\n      // eslint-disable-next-line no-sequences\n      object.events[eventKey].reduceRight((event, l) => (l(event), event), {\n        shared: {},\n        ...eventProps\n      })\n    })\n  }\n  object.events[eventKey] = object.events[eventKey] || []\n  object.events[eventKey].push(listener)\n  return () => {\n    let currentListeners = object.events[eventKey]\n    let index = currentListeners.indexOf(listener)\n    currentListeners.splice(index, 1)\n    if (!currentListeners.length) {\n      delete object.events[eventKey]\n      object.events[eventKey + REVERT_MUTATION]()\n      delete object.events[eventKey + REVERT_MUTATION]\n    }\n  }\n}\n\nlet onStart = ($store, listener) =>\n  on($store, listener, START, runListeners => {\n    let originListen = $store.listen\n    $store.listen = arg => {\n      if (!$store.lc && !$store.starting) {\n        $store.starting = true\n        runListeners()\n        delete $store.starting\n      }\n      return originListen(arg)\n    }\n    return () => {\n      $store.listen = originListen\n    }\n  })\n\nlet onStop = ($store, listener) =>\n  on($store, listener, STOP, runListeners => {\n    let originOff = $store.off\n    $store.off = () => {\n      runListeners()\n      originOff()\n    }\n    return () => {\n      $store.off = originOff\n    }\n  })\n\nlet onSet = ($store, listener) =>\n  on($store, listener, SET, runListeners => {\n    let originSet = $store.set\n    let originSetKey = $store.setKey\n    if ($store.setKey) {\n      $store.setKey = (changed, changedValue) => {\n        let isAborted\n        let abort = () => {\n          isAborted = true\n        }\n\n        runListeners({\n          abort,\n          changed,\n          newValue: { ...$store.value, [changed]: changedValue }\n        })\n        if (!isAborted) return originSetKey(changed, changedValue)\n      }\n    }\n    $store.set = newValue => {\n      let isAborted\n      let abort = () => {\n        isAborted = true\n      }\n\n      runListeners({ abort, newValue })\n      if (!isAborted) return originSet(newValue)\n    }\n    return () => {\n      $store.set = originSet\n      $store.setKey = originSetKey\n    }\n  })\n\nlet onNotify = ($store, listener) =>\n  on($store, listener, NOTIFY, runListeners => {\n    let originNotify = $store.notify\n    $store.notify = (oldValue, changed) => {\n      let isAborted\n      let abort = () => {\n        isAborted = true\n      }\n\n      runListeners({ abort, changed, oldValue })\n      if (!isAborted) return originNotify(oldValue, changed)\n    }\n    return () => {\n      $store.notify = originNotify\n    }\n  })\n\nlet STORE_UNMOUNT_DELAY = 1000\n\nlet onMount = ($store, initialize) => {\n  let listener = payload => {\n    let destroy = initialize(payload)\n    if (destroy) $store.events[UNMOUNT].push(destroy)\n  }\n  return on($store, listener, MOUNT, runListeners => {\n    let originListen = $store.listen\n    $store.listen = (...args) => {\n      if (!$store.lc && !$store.active) {\n        $store.active = true\n        runListeners()\n      }\n      return originListen(...args)\n    }\n\n    let originOff = $store.off\n    $store.events[UNMOUNT] = []\n    $store.off = () => {\n      originOff()\n      setTimeout(() => {\n        if ($store.active && !$store.lc) {\n          $store.active = false\n          for (let destroy of $store.events[UNMOUNT]) destroy()\n          $store.events[UNMOUNT] = []\n        }\n      }, STORE_UNMOUNT_DELAY)\n    }\n\n    if (true) {\n      let originClean = $store[_clean_stores_index_js__WEBPACK_IMPORTED_MODULE_0__.clean]\n      $store[_clean_stores_index_js__WEBPACK_IMPORTED_MODULE_0__.clean] = () => {\n        for (let destroy of $store.events[UNMOUNT]) destroy()\n        $store.events[UNMOUNT] = []\n        $store.active = false\n        originClean()\n      }\n    }\n\n    return () => {\n      $store.listen = originListen\n      $store.off = originOff\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/lifecycle/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/listen-keys/index.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/listen-keys/index.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listenKeys: () => (/* binding */ listenKeys),\n/* harmony export */   subscribeKeys: () => (/* binding */ subscribeKeys)\n/* harmony export */ });\nfunction listenKeys($store, keys, listener) {\n  let keysSet = new Set(keys).add(undefined)\n  return $store.listen((value, oldValue, changed) => {\n    if (keysSet.has(changed)) {\n      listener(value, oldValue, changed)\n    }\n  })\n}\n\nfunction subscribeKeys($store, keys, listener) {\n  let unbind = listenKeys($store, keys, listener)\n  listener($store.value)\n  return unbind\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25hbm9zdG9yZXNAMC4xMS40L25vZGVfbW9kdWxlcy9uYW5vc3RvcmVzL2xpc3Rlbi1rZXlzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUiAxXFxEZXNrdG9wXFxkZXZvdHNwLXYxLjVcXHBvcnRhbHdpemFyZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbmFub3N0b3Jlc0AwLjExLjRcXG5vZGVfbW9kdWxlc1xcbmFub3N0b3Jlc1xcbGlzdGVuLWtleXNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBsaXN0ZW5LZXlzKCRzdG9yZSwga2V5cywgbGlzdGVuZXIpIHtcbiAgbGV0IGtleXNTZXQgPSBuZXcgU2V0KGtleXMpLmFkZCh1bmRlZmluZWQpXG4gIHJldHVybiAkc3RvcmUubGlzdGVuKCh2YWx1ZSwgb2xkVmFsdWUsIGNoYW5nZWQpID0+IHtcbiAgICBpZiAoa2V5c1NldC5oYXMoY2hhbmdlZCkpIHtcbiAgICAgIGxpc3RlbmVyKHZhbHVlLCBvbGRWYWx1ZSwgY2hhbmdlZClcbiAgICB9XG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBzdWJzY3JpYmVLZXlzKCRzdG9yZSwga2V5cywgbGlzdGVuZXIpIHtcbiAgbGV0IHVuYmluZCA9IGxpc3RlbktleXMoJHN0b3JlLCBrZXlzLCBsaXN0ZW5lcilcbiAgbGlzdGVuZXIoJHN0b3JlLnZhbHVlKVxuICByZXR1cm4gdW5iaW5kXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/listen-keys/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/task/index.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/task/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allTasks: () => (/* binding */ allTasks),\n/* harmony export */   cleanTasks: () => (/* binding */ cleanTasks),\n/* harmony export */   startTask: () => (/* binding */ startTask),\n/* harmony export */   task: () => (/* binding */ task)\n/* harmony export */ });\nlet tasks = 0\nlet resolves = []\n\nfunction startTask() {\n  tasks += 1\n  return () => {\n    tasks -= 1\n    if (tasks === 0) {\n      let prevResolves = resolves\n      resolves = []\n      for (let i of prevResolves) i()\n    }\n  }\n}\n\nfunction task(cb) {\n  let endTask = startTask()\n  let promise = cb().finally(endTask)\n  promise.t = true\n  return promise\n}\n\nfunction allTasks() {\n  if (tasks === 0) {\n    return Promise.resolve()\n  } else {\n    return new Promise(resolve => {\n      resolves.push(resolve)\n    })\n  }\n}\n\nfunction cleanTasks() {\n  tasks = 0\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25hbm9zdG9yZXNAMC4xMS40L25vZGVfbW9kdWxlcy9uYW5vc3RvcmVzL3Rhc2svaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUiAxXFxEZXNrdG9wXFxkZXZvdHNwLXYxLjVcXHBvcnRhbHdpemFyZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbmFub3N0b3Jlc0AwLjExLjRcXG5vZGVfbW9kdWxlc1xcbmFub3N0b3Jlc1xcdGFza1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IHRhc2tzID0gMFxubGV0IHJlc29sdmVzID0gW11cblxuZXhwb3J0IGZ1bmN0aW9uIHN0YXJ0VGFzaygpIHtcbiAgdGFza3MgKz0gMVxuICByZXR1cm4gKCkgPT4ge1xuICAgIHRhc2tzIC09IDFcbiAgICBpZiAodGFza3MgPT09IDApIHtcbiAgICAgIGxldCBwcmV2UmVzb2x2ZXMgPSByZXNvbHZlc1xuICAgICAgcmVzb2x2ZXMgPSBbXVxuICAgICAgZm9yIChsZXQgaSBvZiBwcmV2UmVzb2x2ZXMpIGkoKVxuICAgIH1cbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdGFzayhjYikge1xuICBsZXQgZW5kVGFzayA9IHN0YXJ0VGFzaygpXG4gIGxldCBwcm9taXNlID0gY2IoKS5maW5hbGx5KGVuZFRhc2spXG4gIHByb21pc2UudCA9IHRydWVcbiAgcmV0dXJuIHByb21pc2Vcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGFsbFRhc2tzKCkge1xuICBpZiAodGFza3MgPT09IDApIHtcbiAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKClcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UocmVzb2x2ZSA9PiB7XG4gICAgICByZXNvbHZlcy5wdXNoKHJlc29sdmUpXG4gICAgfSlcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gY2xlYW5UYXNrcygpIHtcbiAgdGFza3MgPSAwXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/nanostores@0.11.4/node_modules/nanostores/task/index.js\n");

/***/ })

};
;