"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/agent/transactions/new/page",{

/***/ "(app-pages-browser)/./src/features/transactions/hooks/useTransactionForm.ts":
/*!***************************************************************!*\
  !*** ./src/features/transactions/hooks/useTransactionForm.ts ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTransactionForm: () => (/* binding */ useTransactionForm)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-form */ \"(app-pages-browser)/../../node_modules/.pnpm/@tanstack+react-form@1.11.1_564c9343131f09be0ca7f2d3e5de1772/node_modules/@tanstack/react-form/dist/esm/useForm.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/../../node_modules/.pnpm/@tanstack+react-query@5.75.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/../../node_modules/.pnpm/sonner@2.0.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _trpc_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @trpc/client */ \"(app-pages-browser)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/index.mjs\");\n/* harmony import */ var _utils_trpc__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/trpc */ \"(app-pages-browser)/./src/utils/trpc.ts\");\n/**\n * useTransactionForm Hook\n *\n * Custom hook for managing transaction form state and submission.\n */ \n\n\n\n\nfunction useTransactionForm() {\n    // Set up the mutation for creating a transaction\n    // Set up the mutation for creating a transaction\n    const createTransaction = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        ..._utils_trpc__WEBPACK_IMPORTED_MODULE_2__.trpc.transactions.create.mutationOptions(),\n        onSuccess: {\n            \"useTransactionForm.useMutation[createTransaction]\": ()=>{\n                sonner__WEBPACK_IMPORTED_MODULE_0__.toast.success('Transaction created successfully!');\n            }\n        }[\"useTransactionForm.useMutation[createTransaction]\"],\n        onError: {\n            \"useTransactionForm.useMutation[createTransaction]\": (error)=>{\n                // Handle both TRPCClientError and regular Error types\n                const errorMessage = error instanceof _trpc_client__WEBPACK_IMPORTED_MODULE_1__.TRPCClientError ? error.message : error instanceof Error ? error.message : 'Unknown error';\n                sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Error creating transaction: \".concat(errorMessage));\n            }\n        }[\"useTransactionForm.useMutation[createTransaction]\"]\n    });\n    // Initialize the form with default values\n    // Initialize the form with default values\n    const form = (0,_tanstack_react_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        defaultValues: {\n            // Step 0: Transaction Type & Date\n            marketType: 'secondary',\n            transactionType: '',\n            transactionDate: '',\n            // Step 1-2: Property Details\n            propertyName: '',\n            propertyType: '',\n            address: '',\n            totalPrice: '',\n            monthlyRent: '',\n            propertyDeveloper: '',\n            propertyProject: '',\n            propertyUnitNumber: '',\n            selectedProperty: false,\n            // Additional property details\n            builtUpArea: '',\n            landArea: '',\n            bedrooms: '',\n            bathrooms: '',\n            carParks: '',\n            furnishing: '',\n            propertyFeatures: '',\n            // Step 3: Client Information\n            clientName: '',\n            clientEmail: '',\n            clientPhone: '',\n            clientIdNumber: '',\n            clientAcquisitionSource: '',\n            // Step 4: Co-Broking Setup\n            coBrokingEnabled: false,\n            coBrokingDirection: 'buyer',\n            coBrokingAgentName: '',\n            coBrokingAgentRen: '',\n            coBrokingAgencyName: '',\n            coBrokingAgentContact: '',\n            // Step 5: Commission Calculation\n            commissionValue: '',\n            commissionType: '',\n            commissionPercentage: '',\n            // Step 6: Documents\n            documents: [],\n            // Step 7: Review\n            notes: '',\n            isAgencyListing: false\n        },\n        // Form submission handler\n        onSubmit: {\n            \"useTransactionForm.useForm[form]\": async (param)=>{\n                let { value } = param;\n                try {\n                    // Transform form data to match tRPC schema defined in createTransactionSchema\n                    const transactionData = {\n                        // Step 1: Transaction Type & Date\n                        marketType: value.marketType,\n                        transactionType: value.transactionType,\n                        transactionDate: new Date(value.transactionDate).toISOString(),\n                        // Step 2: Property Details\n                        propertyDetails: {\n                            name: value.propertyName,\n                            type: value.propertyType,\n                            address: value.address,\n                            developer: value.propertyDeveloper || undefined,\n                            project: value.propertyProject || undefined,\n                            unitNumber: value.propertyUnitNumber || undefined\n                        },\n                        // Step 3: Client Information - These must be at root level as per API schema\n                        clientName: value.clientName,\n                        clientEmail: value.clientEmail || \"\",\n                        clientPhone: value.clientPhone || \"\",\n                        clientType: \"buyer\",\n                        clientIdNumber: value.clientIdNumber || \"\",\n                        // Step 4: Co-Broking Setup\n                        coBrokingType: value.coBrokingEnabled ? \"co_broke\" : \"direct\",\n                        coBrokingAgentName: value.coBrokingEnabled ? value.coBrokingAgentName : undefined,\n                        coBrokingAgencyName: value.coBrokingEnabled ? value.coBrokingAgencyName : undefined,\n                        coBrokingAgentRera: value.coBrokingEnabled ? value.coBrokingAgentRen : undefined,\n                        // Step 5: Commission Calculation\n                        totalPrice: value.totalPrice || \"0\",\n                        commissionValue: value.commissionValue || \"0\",\n                        // Handle both fixed_amount (new) and fixed (legacy) values\n                        commissionType: value.commissionType === \"fixed\" ? \"fixed_amount\" : value.commissionType,\n                        commissionPercentage: value.commissionPercentage || undefined,\n                        // Step 6: Notes\n                        notes: value.notes || undefined\n                    };\n                    // Submit the transaction\n                    await createTransaction.mutateAsync(transactionData);\n                    return {\n                        status: 'success'\n                    };\n                } catch (error) {\n                    console.error('Error submitting transaction:', error);\n                    return {\n                        status: 'error',\n                        message: error.message\n                    };\n                }\n            }\n        }[\"useTransactionForm.useForm[form]\"]\n    });\n    return {\n        form,\n        isSubmitting: createTransaction.isPending,\n        isSuccess: createTransaction.isSuccess,\n        error: createTransaction.error\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy90cmFuc2FjdGlvbnMvaG9va3MvdXNlVHJhbnNhY3Rpb25Gb3JtLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBOzs7O0NBSUMsR0FFOEM7QUFDSztBQUNyQjtBQUNnQjtBQUNYO0FBRzdCLFNBQVNLO0lBQ2QsaURBQWlEO0lBQ2pELGlEQUFpRDtJQUNqRCxNQUFNQyxvQkFBb0JMLGtFQUFXQSxDQUFDO1FBQ3BDLEdBQUdHLDZDQUFJQSxDQUFDRyxZQUFZLENBQUNDLE1BQU0sQ0FBQ0MsZUFBZSxFQUFFO1FBQzdDQyxTQUFTO2lFQUFFO2dCQUNUUix5Q0FBS0EsQ0FBQ1MsT0FBTyxDQUFDO1lBQ2hCOztRQUNBQyxPQUFPO2lFQUFFLENBQUNDO2dCQUNSLHNEQUFzRDtnQkFDdEQsTUFBTUMsZUFBZUQsaUJBQWlCVix5REFBZUEsR0FDakRVLE1BQU1FLE9BQU8sR0FDYkYsaUJBQWlCRyxRQUNmSCxNQUFNRSxPQUFPLEdBQ2I7Z0JBQ05iLHlDQUFLQSxDQUFDVyxLQUFLLENBQUMsK0JBQTRDLE9BQWJDO1lBQzdDOztJQUNGO0lBRUEsMENBQTBDO0lBQzFDLDBDQUEwQztJQUMxQyxNQUFNRyxPQUFPakIsNkRBQU9BLENBQUM7UUFDbkJrQixlQUFlO1lBQ2Isa0NBQWtDO1lBQ2xDQyxZQUFZO1lBQ1pDLGlCQUFpQjtZQUNqQkMsaUJBQWlCO1lBRWpCLDZCQUE2QjtZQUM3QkMsY0FBYztZQUNkQyxjQUFjO1lBQ2RDLFNBQVM7WUFDVEMsWUFBWTtZQUNaQyxhQUFhO1lBQ2JDLG1CQUFtQjtZQUNuQkMsaUJBQWlCO1lBQ2pCQyxvQkFBb0I7WUFDcEJDLGtCQUFrQjtZQUVsQiw4QkFBOEI7WUFDOUJDLGFBQWE7WUFDYkMsVUFBVTtZQUNWQyxVQUFVO1lBQ1ZDLFdBQVc7WUFDWEMsVUFBVTtZQUNWQyxZQUFZO1lBQ1pDLGtCQUFrQjtZQUVsQiw2QkFBNkI7WUFDN0JDLFlBQVk7WUFDWkMsYUFBYTtZQUNiQyxhQUFhO1lBQ2JDLGdCQUFnQjtZQUNoQkMseUJBQXlCO1lBRXpCLDJCQUEyQjtZQUMzQkMsa0JBQWtCO1lBQ2xCQyxvQkFBb0I7WUFDcEJDLG9CQUFvQjtZQUNwQkMsbUJBQW1CO1lBQ25CQyxxQkFBcUI7WUFDckJDLHVCQUF1QjtZQUV2QixpQ0FBaUM7WUFDakNDLGlCQUFpQjtZQUNqQkMsZ0JBQWdCO1lBQ2hCQyxzQkFBc0I7WUFFdEIsb0JBQW9CO1lBQ3BCQyxXQUFXLEVBQUU7WUFFYixpQkFBaUI7WUFDakJDLE9BQU87WUFDUEMsaUJBQWlCO1FBQ25CO1FBRUEsMEJBQTBCO1FBQzFCQyxRQUFRO2dEQUFFO29CQUFPLEVBQUVDLEtBQUssRUFBRTtnQkFDeEIsSUFBSTtvQkFDRiw4RUFBOEU7b0JBQzlFLE1BQU1DLGtCQUFrQjt3QkFDdEIsa0NBQWtDO3dCQUNsQ3RDLFlBQVlxQyxNQUFNckMsVUFBVTt3QkFDNUJDLGlCQUFpQm9DLE1BQU1wQyxlQUFlO3dCQUN0Q0MsaUJBQWlCLElBQUlxQyxLQUFLRixNQUFNbkMsZUFBZSxFQUFFc0MsV0FBVzt3QkFFNUQsMkJBQTJCO3dCQUMzQkMsaUJBQWlCOzRCQUNmQyxNQUFNTCxNQUFNbEMsWUFBWTs0QkFDeEJ3QyxNQUFNTixNQUFNakMsWUFBWTs0QkFDeEJDLFNBQVNnQyxNQUFNaEMsT0FBTzs0QkFDdEJ1QyxXQUFXUCxNQUFNN0IsaUJBQWlCLElBQUlxQzs0QkFDdENDLFNBQVNULE1BQU01QixlQUFlLElBQUlvQzs0QkFDbENFLFlBQVlWLE1BQU0zQixrQkFBa0IsSUFBSW1DO3dCQUMxQzt3QkFFQSw2RUFBNkU7d0JBQzdFMUIsWUFBWWtCLE1BQU1sQixVQUFVO3dCQUM1QkMsYUFBYWlCLE1BQU1qQixXQUFXLElBQUk7d0JBQ2xDQyxhQUFhZ0IsTUFBTWhCLFdBQVcsSUFBSTt3QkFDbEMyQixZQUFZO3dCQUNaMUIsZ0JBQWdCZSxNQUFNZixjQUFjLElBQUk7d0JBRXhDLDJCQUEyQjt3QkFDM0IyQixlQUFlWixNQUFNYixnQkFBZ0IsR0FBRyxhQUFhO3dCQUNyREUsb0JBQW9CVyxNQUFNYixnQkFBZ0IsR0FBR2EsTUFBTVgsa0JBQWtCLEdBQUdtQjt3QkFDeEVqQixxQkFBcUJTLE1BQU1iLGdCQUFnQixHQUFHYSxNQUFNVCxtQkFBbUIsR0FBR2lCO3dCQUMxRUssb0JBQW9CYixNQUFNYixnQkFBZ0IsR0FBR2EsTUFBTVYsaUJBQWlCLEdBQUdrQjt3QkFFdkUsaUNBQWlDO3dCQUNqQ3ZDLFlBQVkrQixNQUFNL0IsVUFBVSxJQUFJO3dCQUNoQ3dCLGlCQUFpQk8sTUFBTVAsZUFBZSxJQUFJO3dCQUMxQywyREFBMkQ7d0JBQzNEQyxnQkFBaUIsTUFBT0EsY0FBYyxLQUFnQixVQUFVLGlCQUFpQk0sTUFBTU4sY0FBYzt3QkFDckdDLHNCQUFzQkssTUFBTUwsb0JBQW9CLElBQUlhO3dCQUVwRCxnQkFBZ0I7d0JBQ2hCWCxPQUFPRyxNQUFNSCxLQUFLLElBQUlXO29CQUN4QjtvQkFFQSx5QkFBeUI7b0JBQ3pCLE1BQU0xRCxrQkFBa0JnRSxXQUFXLENBQUNiO29CQUVwQyxPQUFPO3dCQUFFYyxRQUFRO29CQUFVO2dCQUM3QixFQUFFLE9BQU8xRCxPQUFZO29CQUNuQjJELFFBQVEzRCxLQUFLLENBQUMsaUNBQWlDQTtvQkFDL0MsT0FBTzt3QkFBRTBELFFBQVE7d0JBQVN4RCxTQUFTRixNQUFNRSxPQUFPO29CQUFDO2dCQUNuRDtZQUNGOztJQUNGO0lBRUEsT0FBTztRQUNMRTtRQUNBd0QsY0FBY25FLGtCQUFrQm9FLFNBQVM7UUFDekNDLFdBQVdyRSxrQkFBa0JxRSxTQUFTO1FBQ3RDOUQsT0FBT1Asa0JBQWtCTyxLQUFLO0lBQ2hDO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUiAxXFxEZXNrdG9wXFxkZXZvdHNwLXYxLjVcXHBvcnRhbHdpemFyZFxcYXBwc1xcd2ViXFxzcmNcXGZlYXR1cmVzXFx0cmFuc2FjdGlvbnNcXGhvb2tzXFx1c2VUcmFuc2FjdGlvbkZvcm0udHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiB1c2VUcmFuc2FjdGlvbkZvcm0gSG9va1xuICpcbiAqIEN1c3RvbSBob29rIGZvciBtYW5hZ2luZyB0cmFuc2FjdGlvbiBmb3JtIHN0YXRlIGFuZCBzdWJtaXNzaW9uLlxuICovXG5cbmltcG9ydCB7IHVzZUZvcm0gfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtZm9ybSc7XG5pbXBvcnQgeyB1c2VNdXRhdGlvbiB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gJ3Nvbm5lcic7XG5pbXBvcnQgeyBUUlBDQ2xpZW50RXJyb3IgfSBmcm9tICdAdHJwYy9jbGllbnQnO1xuaW1wb3J0IHsgdHJwYyB9IGZyb20gJ0AvdXRpbHMvdHJwYyc7XG5pbXBvcnQgdHlwZSB7IFRyYW5zYWN0aW9uRm9ybVZhbHVlcyB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGZ1bmN0aW9uIHVzZVRyYW5zYWN0aW9uRm9ybSgpIHtcbiAgLy8gU2V0IHVwIHRoZSBtdXRhdGlvbiBmb3IgY3JlYXRpbmcgYSB0cmFuc2FjdGlvblxuICAvLyBTZXQgdXAgdGhlIG11dGF0aW9uIGZvciBjcmVhdGluZyBhIHRyYW5zYWN0aW9uXG4gIGNvbnN0IGNyZWF0ZVRyYW5zYWN0aW9uID0gdXNlTXV0YXRpb24oe1xuICAgIC4uLnRycGMudHJhbnNhY3Rpb25zLmNyZWF0ZS5tdXRhdGlvbk9wdGlvbnMoKSxcbiAgICBvblN1Y2Nlc3M6ICgpID0+IHtcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ1RyYW5zYWN0aW9uIGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5IScpO1xuICAgIH0sXG4gICAgb25FcnJvcjogKGVycm9yKSA9PiB7XG4gICAgICAvLyBIYW5kbGUgYm90aCBUUlBDQ2xpZW50RXJyb3IgYW5kIHJlZ3VsYXIgRXJyb3IgdHlwZXNcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgVFJQQ0NsaWVudEVycm9yXG4gICAgICAgID8gZXJyb3IubWVzc2FnZVxuICAgICAgICA6IGVycm9yIGluc3RhbmNlb2YgRXJyb3JcbiAgICAgICAgICA/IGVycm9yLm1lc3NhZ2VcbiAgICAgICAgICA6ICdVbmtub3duIGVycm9yJztcbiAgICAgIHRvYXN0LmVycm9yKGBFcnJvciBjcmVhdGluZyB0cmFuc2FjdGlvbjogJHtlcnJvck1lc3NhZ2V9YCk7XG4gICAgfVxuICB9KTtcblxuICAvLyBJbml0aWFsaXplIHRoZSBmb3JtIHdpdGggZGVmYXVsdCB2YWx1ZXNcbiAgLy8gSW5pdGlhbGl6ZSB0aGUgZm9ybSB3aXRoIGRlZmF1bHQgdmFsdWVzXG4gIGNvbnN0IGZvcm0gPSB1c2VGb3JtKHtcbiAgICBkZWZhdWx0VmFsdWVzOiB7XG4gICAgICAvLyBTdGVwIDA6IFRyYW5zYWN0aW9uIFR5cGUgJiBEYXRlXG4gICAgICBtYXJrZXRUeXBlOiAnc2Vjb25kYXJ5JyxcbiAgICAgIHRyYW5zYWN0aW9uVHlwZTogJycsXG4gICAgICB0cmFuc2FjdGlvbkRhdGU6ICcnLFxuXG4gICAgICAvLyBTdGVwIDEtMjogUHJvcGVydHkgRGV0YWlsc1xuICAgICAgcHJvcGVydHlOYW1lOiAnJyxcbiAgICAgIHByb3BlcnR5VHlwZTogJycsXG4gICAgICBhZGRyZXNzOiAnJyxcbiAgICAgIHRvdGFsUHJpY2U6ICcnLFxuICAgICAgbW9udGhseVJlbnQ6ICcnLCAvLyBGb3IgbGVhc2UgdHJhbnNhY3Rpb25zXG4gICAgICBwcm9wZXJ0eURldmVsb3BlcjogJycsXG4gICAgICBwcm9wZXJ0eVByb2plY3Q6ICcnLFxuICAgICAgcHJvcGVydHlVbml0TnVtYmVyOiAnJywgLy8gTmV3IGZpZWxkIGZvciBwcm9wZXJ0eSB1bml0IG51bWJlclxuICAgICAgc2VsZWN0ZWRQcm9wZXJ0eTogZmFsc2UsIC8vIFRyYWNraW5nIHByb3BlcnR5IHNlbGVjdGlvbiBmb3IgUHJpbWFyeSBNYXJrZXRcblxuICAgICAgLy8gQWRkaXRpb25hbCBwcm9wZXJ0eSBkZXRhaWxzXG4gICAgICBidWlsdFVwQXJlYTogJycsXG4gICAgICBsYW5kQXJlYTogJycsXG4gICAgICBiZWRyb29tczogJycsXG4gICAgICBiYXRocm9vbXM6ICcnLFxuICAgICAgY2FyUGFya3M6ICcnLFxuICAgICAgZnVybmlzaGluZzogJycsXG4gICAgICBwcm9wZXJ0eUZlYXR1cmVzOiAnJyxcblxuICAgICAgLy8gU3RlcCAzOiBDbGllbnQgSW5mb3JtYXRpb25cbiAgICAgIGNsaWVudE5hbWU6ICcnLFxuICAgICAgY2xpZW50RW1haWw6ICcnLFxuICAgICAgY2xpZW50UGhvbmU6ICcnLFxuICAgICAgY2xpZW50SWROdW1iZXI6ICcnLFxuICAgICAgY2xpZW50QWNxdWlzaXRpb25Tb3VyY2U6ICcnLFxuXG4gICAgICAvLyBTdGVwIDQ6IENvLUJyb2tpbmcgU2V0dXBcbiAgICAgIGNvQnJva2luZ0VuYWJsZWQ6IGZhbHNlLFxuICAgICAgY29Ccm9raW5nRGlyZWN0aW9uOiAnYnV5ZXInIGFzICdzZWxsZXInIHwgJ2J1eWVyJyxcbiAgICAgIGNvQnJva2luZ0FnZW50TmFtZTogJycsXG4gICAgICBjb0Jyb2tpbmdBZ2VudFJlbjogJycsXG4gICAgICBjb0Jyb2tpbmdBZ2VuY3lOYW1lOiAnJyxcbiAgICAgIGNvQnJva2luZ0FnZW50Q29udGFjdDogJycsXG5cbiAgICAgIC8vIFN0ZXAgNTogQ29tbWlzc2lvbiBDYWxjdWxhdGlvblxuICAgICAgY29tbWlzc2lvblZhbHVlOiAnJyxcbiAgICAgIGNvbW1pc3Npb25UeXBlOiAnJyxcbiAgICAgIGNvbW1pc3Npb25QZXJjZW50YWdlOiAnJyxcblxuICAgICAgLy8gU3RlcCA2OiBEb2N1bWVudHNcbiAgICAgIGRvY3VtZW50czogW10gYXMgRmlsZVtdLFxuXG4gICAgICAvLyBTdGVwIDc6IFJldmlld1xuICAgICAgbm90ZXM6ICcnLFxuICAgICAgaXNBZ2VuY3lMaXN0aW5nOiBmYWxzZSxcbiAgICB9IGFzIFRyYW5zYWN0aW9uRm9ybVZhbHVlcyxcblxuICAgIC8vIEZvcm0gc3VibWlzc2lvbiBoYW5kbGVyXG4gICAgb25TdWJtaXQ6IGFzeW5jICh7IHZhbHVlIH0pID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIFRyYW5zZm9ybSBmb3JtIGRhdGEgdG8gbWF0Y2ggdFJQQyBzY2hlbWEgZGVmaW5lZCBpbiBjcmVhdGVUcmFuc2FjdGlvblNjaGVtYVxuICAgICAgICBjb25zdCB0cmFuc2FjdGlvbkRhdGEgPSB7XG4gICAgICAgICAgLy8gU3RlcCAxOiBUcmFuc2FjdGlvbiBUeXBlICYgRGF0ZVxuICAgICAgICAgIG1hcmtldFR5cGU6IHZhbHVlLm1hcmtldFR5cGUgYXMgXCJwcmltYXJ5XCIgfCBcInNlY29uZGFyeVwiLFxuICAgICAgICAgIHRyYW5zYWN0aW9uVHlwZTogdmFsdWUudHJhbnNhY3Rpb25UeXBlIGFzIFwic2FsZVwiIHwgXCJsZWFzZVwiLFxuICAgICAgICAgIHRyYW5zYWN0aW9uRGF0ZTogbmV3IERhdGUodmFsdWUudHJhbnNhY3Rpb25EYXRlKS50b0lTT1N0cmluZygpLFxuXG4gICAgICAgICAgLy8gU3RlcCAyOiBQcm9wZXJ0eSBEZXRhaWxzXG4gICAgICAgICAgcHJvcGVydHlEZXRhaWxzOiB7XG4gICAgICAgICAgICBuYW1lOiB2YWx1ZS5wcm9wZXJ0eU5hbWUsXG4gICAgICAgICAgICB0eXBlOiB2YWx1ZS5wcm9wZXJ0eVR5cGUgYXMgXCJyZXNpZGVudGlhbF92aWxsYVwiIHwgXCJyZXNpZGVudGlhbF9hcGFydG1lbnRcIiB8IFwicmVzaWRlbnRpYWxfdG93bmhvdXNlXCIgfCBcImNvbW1lcmNpYWxfb2ZmaWNlXCIgfCBcImNvbW1lcmNpYWxfcmV0YWlsXCIgfCBcImNvbW1lcmNpYWxfd2FyZWhvdXNlXCIgfCBcImxhbmRfcmVzaWRlbnRpYWxcIiB8IFwibGFuZF9jb21tZXJjaWFsXCIgfCBcImluZHVzdHJpYWxfZmFjdG9yeVwiIHwgXCJvdGhlclwiLFxuICAgICAgICAgICAgYWRkcmVzczogdmFsdWUuYWRkcmVzcyxcbiAgICAgICAgICAgIGRldmVsb3BlcjogdmFsdWUucHJvcGVydHlEZXZlbG9wZXIgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgICAgcHJvamVjdDogdmFsdWUucHJvcGVydHlQcm9qZWN0IHx8IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIHVuaXROdW1iZXI6IHZhbHVlLnByb3BlcnR5VW5pdE51bWJlciB8fCB1bmRlZmluZWQsIC8vIE5ldyBmaWVsZFxuICAgICAgICAgIH0sXG5cbiAgICAgICAgICAvLyBTdGVwIDM6IENsaWVudCBJbmZvcm1hdGlvbiAtIFRoZXNlIG11c3QgYmUgYXQgcm9vdCBsZXZlbCBhcyBwZXIgQVBJIHNjaGVtYVxuICAgICAgICAgIGNsaWVudE5hbWU6IHZhbHVlLmNsaWVudE5hbWUsXG4gICAgICAgICAgY2xpZW50RW1haWw6IHZhbHVlLmNsaWVudEVtYWlsIHx8IFwiXCIsXG4gICAgICAgICAgY2xpZW50UGhvbmU6IHZhbHVlLmNsaWVudFBob25lIHx8IFwiXCIsXG4gICAgICAgICAgY2xpZW50VHlwZTogXCJidXllclwiIGFzIFwiYnV5ZXJcIiB8IFwic2VsbGVyXCIgfCBcInRlbmFudFwiIHwgXCJsYW5kbG9yZFwiLCAvLyBEZWZhdWx0IHRvIGJ1eWVyLCBzaG91bGQgYmUgc2V0IGJhc2VkIG9uIGZvcm1cbiAgICAgICAgICBjbGllbnRJZE51bWJlcjogdmFsdWUuY2xpZW50SWROdW1iZXIgfHwgXCJcIixcblxuICAgICAgICAgIC8vIFN0ZXAgNDogQ28tQnJva2luZyBTZXR1cFxuICAgICAgICAgIGNvQnJva2luZ1R5cGU6IHZhbHVlLmNvQnJva2luZ0VuYWJsZWQgPyBcImNvX2Jyb2tlXCIgOiBcImRpcmVjdFwiIGFzIFwiZGlyZWN0XCIgfCBcImNvX2Jyb2tlXCIsXG4gICAgICAgICAgY29Ccm9raW5nQWdlbnROYW1lOiB2YWx1ZS5jb0Jyb2tpbmdFbmFibGVkID8gdmFsdWUuY29Ccm9raW5nQWdlbnROYW1lIDogdW5kZWZpbmVkLFxuICAgICAgICAgIGNvQnJva2luZ0FnZW5jeU5hbWU6IHZhbHVlLmNvQnJva2luZ0VuYWJsZWQgPyB2YWx1ZS5jb0Jyb2tpbmdBZ2VuY3lOYW1lIDogdW5kZWZpbmVkLFxuICAgICAgICAgIGNvQnJva2luZ0FnZW50UmVyYTogdmFsdWUuY29Ccm9raW5nRW5hYmxlZCA/IHZhbHVlLmNvQnJva2luZ0FnZW50UmVuIDogdW5kZWZpbmVkLFxuXG4gICAgICAgICAgLy8gU3RlcCA1OiBDb21taXNzaW9uIENhbGN1bGF0aW9uXG4gICAgICAgICAgdG90YWxQcmljZTogdmFsdWUudG90YWxQcmljZSB8fCBcIjBcIixcbiAgICAgICAgICBjb21taXNzaW9uVmFsdWU6IHZhbHVlLmNvbW1pc3Npb25WYWx1ZSB8fCBcIjBcIixcbiAgICAgICAgICAvLyBIYW5kbGUgYm90aCBmaXhlZF9hbW91bnQgKG5ldykgYW5kIGZpeGVkIChsZWdhY3kpIHZhbHVlc1xuICAgICAgICAgIGNvbW1pc3Npb25UeXBlOiAoKHZhbHVlLmNvbW1pc3Npb25UeXBlIGFzIHN0cmluZykgPT09IFwiZml4ZWRcIiA/IFwiZml4ZWRfYW1vdW50XCIgOiB2YWx1ZS5jb21taXNzaW9uVHlwZSkgYXMgXCJwZXJjZW50YWdlXCIgfCBcImZpeGVkX2Ftb3VudFwiLFxuICAgICAgICAgIGNvbW1pc3Npb25QZXJjZW50YWdlOiB2YWx1ZS5jb21taXNzaW9uUGVyY2VudGFnZSB8fCB1bmRlZmluZWQsXG5cbiAgICAgICAgICAvLyBTdGVwIDY6IE5vdGVzXG4gICAgICAgICAgbm90ZXM6IHZhbHVlLm5vdGVzIHx8IHVuZGVmaW5lZCxcbiAgICAgICAgfTtcblxuICAgICAgICAvLyBTdWJtaXQgdGhlIHRyYW5zYWN0aW9uXG4gICAgICAgIGF3YWl0IGNyZWF0ZVRyYW5zYWN0aW9uLm11dGF0ZUFzeW5jKHRyYW5zYWN0aW9uRGF0YSk7XG5cbiAgICAgICAgcmV0dXJuIHsgc3RhdHVzOiAnc3VjY2VzcycgfTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc3VibWl0dGluZyB0cmFuc2FjdGlvbjonLCBlcnJvcik7XG4gICAgICAgIHJldHVybiB7IHN0YXR1czogJ2Vycm9yJywgbWVzc2FnZTogZXJyb3IubWVzc2FnZSB9O1xuICAgICAgfVxuICAgIH0sXG4gIH0pO1xuXG4gIHJldHVybiB7XG4gICAgZm9ybSxcbiAgICBpc1N1Ym1pdHRpbmc6IGNyZWF0ZVRyYW5zYWN0aW9uLmlzUGVuZGluZyxcbiAgICBpc1N1Y2Nlc3M6IGNyZWF0ZVRyYW5zYWN0aW9uLmlzU3VjY2VzcyxcbiAgICBlcnJvcjogY3JlYXRlVHJhbnNhY3Rpb24uZXJyb3IsXG4gIH07XG59XG4iXSwibmFtZXMiOlsidXNlRm9ybSIsInVzZU11dGF0aW9uIiwidG9hc3QiLCJUUlBDQ2xpZW50RXJyb3IiLCJ0cnBjIiwidXNlVHJhbnNhY3Rpb25Gb3JtIiwiY3JlYXRlVHJhbnNhY3Rpb24iLCJ0cmFuc2FjdGlvbnMiLCJjcmVhdGUiLCJtdXRhdGlvbk9wdGlvbnMiLCJvblN1Y2Nlc3MiLCJzdWNjZXNzIiwib25FcnJvciIsImVycm9yIiwiZXJyb3JNZXNzYWdlIiwibWVzc2FnZSIsIkVycm9yIiwiZm9ybSIsImRlZmF1bHRWYWx1ZXMiLCJtYXJrZXRUeXBlIiwidHJhbnNhY3Rpb25UeXBlIiwidHJhbnNhY3Rpb25EYXRlIiwicHJvcGVydHlOYW1lIiwicHJvcGVydHlUeXBlIiwiYWRkcmVzcyIsInRvdGFsUHJpY2UiLCJtb250aGx5UmVudCIsInByb3BlcnR5RGV2ZWxvcGVyIiwicHJvcGVydHlQcm9qZWN0IiwicHJvcGVydHlVbml0TnVtYmVyIiwic2VsZWN0ZWRQcm9wZXJ0eSIsImJ1aWx0VXBBcmVhIiwibGFuZEFyZWEiLCJiZWRyb29tcyIsImJhdGhyb29tcyIsImNhclBhcmtzIiwiZnVybmlzaGluZyIsInByb3BlcnR5RmVhdHVyZXMiLCJjbGllbnROYW1lIiwiY2xpZW50RW1haWwiLCJjbGllbnRQaG9uZSIsImNsaWVudElkTnVtYmVyIiwiY2xpZW50QWNxdWlzaXRpb25Tb3VyY2UiLCJjb0Jyb2tpbmdFbmFibGVkIiwiY29Ccm9raW5nRGlyZWN0aW9uIiwiY29Ccm9raW5nQWdlbnROYW1lIiwiY29Ccm9raW5nQWdlbnRSZW4iLCJjb0Jyb2tpbmdBZ2VuY3lOYW1lIiwiY29Ccm9raW5nQWdlbnRDb250YWN0IiwiY29tbWlzc2lvblZhbHVlIiwiY29tbWlzc2lvblR5cGUiLCJjb21taXNzaW9uUGVyY2VudGFnZSIsImRvY3VtZW50cyIsIm5vdGVzIiwiaXNBZ2VuY3lMaXN0aW5nIiwib25TdWJtaXQiLCJ2YWx1ZSIsInRyYW5zYWN0aW9uRGF0YSIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInByb3BlcnR5RGV0YWlscyIsIm5hbWUiLCJ0eXBlIiwiZGV2ZWxvcGVyIiwidW5kZWZpbmVkIiwicHJvamVjdCIsInVuaXROdW1iZXIiLCJjbGllbnRUeXBlIiwiY29Ccm9raW5nVHlwZSIsImNvQnJva2luZ0FnZW50UmVyYSIsIm11dGF0ZUFzeW5jIiwic3RhdHVzIiwiY29uc29sZSIsImlzU3VibWl0dGluZyIsImlzUGVuZGluZyIsImlzU3VjY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/transactions/hooks/useTransactionForm.ts\n"));

/***/ })

});