"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack+react-store@0.7.0_e9ef76b6644c2d62a4d0cb38edf9a312";
exports.ids = ["vendor-chunks/@tanstack+react-store@0.7.0_e9ef76b6644c2d62a4d0cb38edf9a312"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-store@0.7.0_e9ef76b6644c2d62a4d0cb38edf9a312/node_modules/@tanstack/react-store/dist/esm/index.js":
/*!**************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-store@0.7.0_e9ef76b6644c2d62a4d0cb38edf9a312/node_modules/@tanstack/react-store/dist/esm/index.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Derived: () => (/* reexport safe */ _tanstack_store__WEBPACK_IMPORTED_MODULE_1__.Derived),\n/* harmony export */   Effect: () => (/* reexport safe */ _tanstack_store__WEBPACK_IMPORTED_MODULE_1__.Effect),\n/* harmony export */   Store: () => (/* reexport safe */ _tanstack_store__WEBPACK_IMPORTED_MODULE_1__.Store),\n/* harmony export */   __depsThatHaveWrittenThisTick: () => (/* reexport safe */ _tanstack_store__WEBPACK_IMPORTED_MODULE_1__.__depsThatHaveWrittenThisTick),\n/* harmony export */   __derivedToStore: () => (/* reexport safe */ _tanstack_store__WEBPACK_IMPORTED_MODULE_1__.__derivedToStore),\n/* harmony export */   __flush: () => (/* reexport safe */ _tanstack_store__WEBPACK_IMPORTED_MODULE_1__.__flush),\n/* harmony export */   __storeToDerived: () => (/* reexport safe */ _tanstack_store__WEBPACK_IMPORTED_MODULE_1__.__storeToDerived),\n/* harmony export */   batch: () => (/* reexport safe */ _tanstack_store__WEBPACK_IMPORTED_MODULE_1__.batch),\n/* harmony export */   shallow: () => (/* binding */ shallow),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sync-external-store/shim/with-selector.js */ \"(ssr)/../../node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/shim/with-selector.js\");\n/* harmony import */ var _tanstack_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/store */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/index.js\");\n\n\nfunction useStore(store, selector = (d) => d) {\n  const slice = (0,use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStoreWithSelector)(\n    store.subscribe,\n    () => store.state,\n    () => store.state,\n    selector,\n    shallow\n  );\n  return slice;\n}\nfunction shallow(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false;\n    for (const [k, v] of objA) {\n      if (!objB.has(k) || !Object.is(v, objB.get(k))) return false;\n    }\n    return true;\n  }\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false;\n    for (const v of objA) {\n      if (!objB.has(v)) return false;\n    }\n    return true;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (let i = 0; i < keysA.length; i++) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !Object.is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n  return true;\n}\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-store@0.7.0_e9ef76b6644c2d62a4d0cb38edf9a312/node_modules/@tanstack/react-store/dist/esm/index.js\n");

/***/ })

};
;