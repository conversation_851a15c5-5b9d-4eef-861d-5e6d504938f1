"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-avatar@1.1._94851af4e4100d6d569005fc9e9e41cd";
exports.ids = ["vendor-chunks/@radix-ui+react-avatar@1.1._94851af4e4100d6d569005fc9e9e41cd"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-avatar@1.1._94851af4e4100d6d569005fc9e9e41cd/node_modules/@radix-ui/react-avatar/dist/index.mjs":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-avatar@1.1._94851af4e4100d6d569005fc9e9e41cd/node_modules/@radix-ui/react-avatar/dist/index.mjs ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage),\n/* harmony export */   Fallback: () => (/* binding */ Fallback),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createAvatarScope: () => (/* binding */ createAvatarScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1_a8f58efc49c849094cf13e0699195f5f/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-callbac_47c065e45719ebf277832e97251a8e29/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-layout-_66f6001c9fe78a4b49c68b1f72e6db1a/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-primitive@2_e1a155cffd5059c05e9aa59be25b95f1/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_is_hydrated__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-is-hydrated */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-is-hydr_e6ed817509fef5312276c1fbbc3a20ee/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarFallback,AvatarImage,Fallback,Image,Root,createAvatarScope auto */ // src/avatar.tsx\n\n\n\n\n\n\n\nvar AVATAR_NAME = \"Avatar\";\nvar [createAvatarContext, createAvatarScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(AVATAR_NAME);\nvar [AvatarProvider, useAvatarContext] = createAvatarContext(AVATAR_NAME);\nvar Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AvatarProvider, {\n        scope: __scopeAvatar,\n        imageLoadingStatus,\n        onImageLoadingStatusChange: setImageLoadingStatus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n            ...avatarProps,\n            ref: forwardedRef\n        })\n    });\n});\nAvatar.displayName = AVATAR_NAME;\nvar IMAGE_NAME = \"AvatarImage\";\nvar AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, src, onLoadingStatusChange = ()=>{}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);\n    const handleLoadingStatusChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useCallbackRef)({\n        \"AvatarImage.useCallbackRef[handleLoadingStatusChange]\": (status)=>{\n            onLoadingStatusChange(status);\n            context.onImageLoadingStatusChange(status);\n        }\n    }[\"AvatarImage.useCallbackRef[handleLoadingStatusChange]\"]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"AvatarImage.useLayoutEffect\": ()=>{\n            if (imageLoadingStatus !== \"idle\") {\n                handleLoadingStatusChange(imageLoadingStatus);\n            }\n        }\n    }[\"AvatarImage.useLayoutEffect\"], [\n        imageLoadingStatus,\n        handleLoadingStatusChange\n    ]);\n    return imageLoadingStatus === \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.img, {\n        ...imageProps,\n        ref: forwardedRef,\n        src\n    }) : null;\n});\nAvatarImage.displayName = IMAGE_NAME;\nvar FALLBACK_NAME = \"AvatarFallback\";\nvar AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = react__WEBPACK_IMPORTED_MODULE_0__.useState(delayMs === void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"AvatarFallback.useEffect\": ()=>{\n            if (delayMs !== void 0) {\n                const timerId = window.setTimeout({\n                    \"AvatarFallback.useEffect.timerId\": ()=>setCanRender(true)\n                }[\"AvatarFallback.useEffect.timerId\"], delayMs);\n                return ({\n                    \"AvatarFallback.useEffect\": ()=>window.clearTimeout(timerId)\n                })[\"AvatarFallback.useEffect\"];\n            }\n        }\n    }[\"AvatarFallback.useEffect\"], [\n        delayMs\n    ]);\n    return canRender && context.imageLoadingStatus !== \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n        ...fallbackProps,\n        ref: forwardedRef\n    }) : null;\n});\nAvatarFallback.displayName = FALLBACK_NAME;\nfunction resolveLoadingStatus(image, src) {\n    if (!image) {\n        return \"idle\";\n    }\n    if (!src) {\n        return \"error\";\n    }\n    if (image.src !== src) {\n        image.src = src;\n    }\n    return image.complete && image.naturalWidth > 0 ? \"loaded\" : \"loading\";\n}\nfunction useImageLoadingStatus(src, { referrerPolicy, crossOrigin }) {\n    const isHydrated = (0,_radix_ui_react_use_is_hydrated__WEBPACK_IMPORTED_MODULE_6__.useIsHydrated)();\n    const imageRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const image = (()=>{\n        if (!isHydrated) return null;\n        if (!imageRef.current) {\n            imageRef.current = new window.Image();\n        }\n        return imageRef.current;\n    })();\n    const [loadingStatus, setLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"useImageLoadingStatus.useState\": ()=>resolveLoadingStatus(image, src)\n    }[\"useImageLoadingStatus.useState\"]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n            setLoadingStatus(resolveLoadingStatus(image, src));\n        }\n    }[\"useImageLoadingStatus.useLayoutEffect\"], [\n        image,\n        src\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n            const updateStatus = {\n                \"useImageLoadingStatus.useLayoutEffect.updateStatus\": (status)=>({\n                        \"useImageLoadingStatus.useLayoutEffect.updateStatus\": ()=>{\n                            setLoadingStatus(status);\n                        }\n                    })[\"useImageLoadingStatus.useLayoutEffect.updateStatus\"]\n            }[\"useImageLoadingStatus.useLayoutEffect.updateStatus\"];\n            if (!image) return;\n            const handleLoad = updateStatus(\"loaded\");\n            const handleError = updateStatus(\"error\");\n            image.addEventListener(\"load\", handleLoad);\n            image.addEventListener(\"error\", handleError);\n            if (referrerPolicy) {\n                image.referrerPolicy = referrerPolicy;\n            }\n            if (typeof crossOrigin === \"string\") {\n                image.crossOrigin = crossOrigin;\n            }\n            return ({\n                \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n                    image.removeEventListener(\"load\", handleLoad);\n                    image.removeEventListener(\"error\", handleError);\n                }\n            })[\"useImageLoadingStatus.useLayoutEffect\"];\n        }\n    }[\"useImageLoadingStatus.useLayoutEffect\"], [\n        image,\n        crossOrigin,\n        referrerPolicy\n    ]);\n    return loadingStatus;\n}\nvar Root = Avatar;\nvar Image = AvatarImage;\nvar Fallback = AvatarFallback;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-avatar@1.1._94851af4e4100d6d569005fc9e9e41cd/node_modules/@radix-ui/react-avatar/dist/index.mjs\n");

/***/ })

};
;