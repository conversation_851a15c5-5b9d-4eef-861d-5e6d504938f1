"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34";
exports.ids = ["vendor-chunks/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/TRPCClientError.mjs":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/TRPCClientError.mjs ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TRPCClientError: () => (/* binding */ TRPCClientError)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/unstable-core-do-not-import */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/unstable-core-do-not-import.mjs\");\n\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction isTRPCClientError(cause) {\n    return cause instanceof TRPCClientError || /**\n     * @deprecated\n     * Delete in next major\n     */ cause instanceof Error && cause.name === 'TRPCClientError';\n}\nfunction isTRPCErrorResponse(obj) {\n    return (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_0__.isObject)(obj) && (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_0__.isObject)(obj['error']) && typeof obj['error']['code'] === 'number' && typeof obj['error']['message'] === 'string';\n}\nfunction getMessageFromUnknownError(err, fallback) {\n    if (typeof err === 'string') {\n        return err;\n    }\n    if ((0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_0__.isObject)(err) && typeof err['message'] === 'string') {\n        return err['message'];\n    }\n    return fallback;\n}\nclass TRPCClientError extends Error {\n    static from(_cause, opts = {}) {\n        const cause = _cause;\n        if (isTRPCClientError(cause)) {\n            if (opts.meta) {\n                // Decorate with meta error data\n                cause.meta = {\n                    ...cause.meta,\n                    ...opts.meta\n                };\n            }\n            return cause;\n        }\n        if (isTRPCErrorResponse(cause)) {\n            return new TRPCClientError(cause.error.message, {\n                ...opts,\n                result: cause\n            });\n        }\n        return new TRPCClientError(getMessageFromUnknownError(cause, 'Unknown error'), {\n            ...opts,\n            cause: cause\n        });\n    }\n    constructor(message, opts){\n        const cause = opts?.cause;\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore https://github.com/tc39/proposal-error-cause\n        super(message, {\n            cause\n        }), // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore override doesn't work in all environments due to \"This member cannot have an 'override' modifier because it is not declared in the base class 'Error'\"\n        _define_property(this, \"cause\", void 0), _define_property(this, \"shape\", void 0), _define_property(this, \"data\", void 0), /**\n   * Additional meta data about the error\n   * In the case of HTTP-errors, we'll have `response` and potentially `responseJSON` here\n   */ _define_property(this, \"meta\", void 0);\n        this.meta = opts?.meta;\n        this.cause = cause;\n        this.shape = opts?.result?.error;\n        this.data = opts?.result?.error.data;\n        this.name = 'TRPCClientError';\n        Object.setPrototypeOf(this, TRPCClientError.prototype);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/TRPCClientError.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/createTRPCClient.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/createTRPCClient.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clientCallTypeToProcedureType: () => (/* binding */ clientCallTypeToProcedureType),\n/* harmony export */   createTRPCClient: () => (/* binding */ createTRPCClient),\n/* harmony export */   createTRPCClientProxy: () => (/* binding */ createTRPCClientProxy),\n/* harmony export */   getUntypedClient: () => (/* binding */ getUntypedClient)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/unstable-core-do-not-import */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/unstable-core-do-not-import.mjs\");\n/* harmony import */ var _internals_TRPCUntypedClient_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./internals/TRPCUntypedClient.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/TRPCUntypedClient.mjs\");\n\n\n\nconst untypedClientSymbol = Symbol.for('trpc_untypedClient');\nconst clientCallTypeMap = {\n    query: 'query',\n    mutate: 'mutation',\n    subscribe: 'subscription'\n};\n/** @internal */ const clientCallTypeToProcedureType = (clientCallType)=>{\n    return clientCallTypeMap[clientCallType];\n};\n/**\n * @internal\n */ function createTRPCClientProxy(client) {\n    const proxy = (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_0__.createRecursiveProxy)(({ path, args })=>{\n        const pathCopy = [\n            ...path\n        ];\n        const procedureType = clientCallTypeToProcedureType(pathCopy.pop());\n        const fullPath = pathCopy.join('.');\n        return client[procedureType](fullPath, ...args);\n    });\n    return (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_0__.createFlatProxy)((key)=>{\n        if (key === untypedClientSymbol) {\n            return client;\n        }\n        return proxy[key];\n    });\n}\nfunction createTRPCClient(opts) {\n    const client = new _internals_TRPCUntypedClient_mjs__WEBPACK_IMPORTED_MODULE_1__.TRPCUntypedClient(opts);\n    const proxy = createTRPCClientProxy(client);\n    return proxy;\n}\n/**\n * Get an untyped client from a proxy client\n * @internal\n */ function getUntypedClient(client) {\n    return client[untypedClientSymbol];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNC9ub2RlX21vZHVsZXMvQHRycGMvY2xpZW50L2Rpc3QvY3JlYXRlVFJQQ0NsaWVudC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQWlHO0FBQzNCOztBQUV0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsOEZBQW9CLElBQUksWUFBWTtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsV0FBVyx5RkFBZTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsdUJBQXVCLCtFQUFpQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW9HIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVIgMVxcRGVza3RvcFxcZGV2b3RzcC12MS41XFxwb3J0YWx3aXphcmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEB0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNFxcbm9kZV9tb2R1bGVzXFxAdHJwY1xcY2xpZW50XFxkaXN0XFxjcmVhdGVUUlBDQ2xpZW50Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVSZWN1cnNpdmVQcm94eSwgY3JlYXRlRmxhdFByb3h5IH0gZnJvbSAnQHRycGMvc2VydmVyL3Vuc3RhYmxlLWNvcmUtZG8tbm90LWltcG9ydCc7XG5pbXBvcnQgeyBUUlBDVW50eXBlZENsaWVudCB9IGZyb20gJy4vaW50ZXJuYWxzL1RSUENVbnR5cGVkQ2xpZW50Lm1qcyc7XG5cbmNvbnN0IHVudHlwZWRDbGllbnRTeW1ib2wgPSBTeW1ib2wuZm9yKCd0cnBjX3VudHlwZWRDbGllbnQnKTtcbmNvbnN0IGNsaWVudENhbGxUeXBlTWFwID0ge1xuICAgIHF1ZXJ5OiAncXVlcnknLFxuICAgIG11dGF0ZTogJ211dGF0aW9uJyxcbiAgICBzdWJzY3JpYmU6ICdzdWJzY3JpcHRpb24nXG59O1xuLyoqIEBpbnRlcm5hbCAqLyBjb25zdCBjbGllbnRDYWxsVHlwZVRvUHJvY2VkdXJlVHlwZSA9IChjbGllbnRDYWxsVHlwZSk9PntcbiAgICByZXR1cm4gY2xpZW50Q2FsbFR5cGVNYXBbY2xpZW50Q2FsbFR5cGVdO1xufTtcbi8qKlxuICogQGludGVybmFsXG4gKi8gZnVuY3Rpb24gY3JlYXRlVFJQQ0NsaWVudFByb3h5KGNsaWVudCkge1xuICAgIGNvbnN0IHByb3h5ID0gY3JlYXRlUmVjdXJzaXZlUHJveHkoKHsgcGF0aCwgYXJncyB9KT0+e1xuICAgICAgICBjb25zdCBwYXRoQ29weSA9IFtcbiAgICAgICAgICAgIC4uLnBhdGhcbiAgICAgICAgXTtcbiAgICAgICAgY29uc3QgcHJvY2VkdXJlVHlwZSA9IGNsaWVudENhbGxUeXBlVG9Qcm9jZWR1cmVUeXBlKHBhdGhDb3B5LnBvcCgpKTtcbiAgICAgICAgY29uc3QgZnVsbFBhdGggPSBwYXRoQ29weS5qb2luKCcuJyk7XG4gICAgICAgIHJldHVybiBjbGllbnRbcHJvY2VkdXJlVHlwZV0oZnVsbFBhdGgsIC4uLmFyZ3MpO1xuICAgIH0pO1xuICAgIHJldHVybiBjcmVhdGVGbGF0UHJveHkoKGtleSk9PntcbiAgICAgICAgaWYgKGtleSA9PT0gdW50eXBlZENsaWVudFN5bWJvbCkge1xuICAgICAgICAgICAgcmV0dXJuIGNsaWVudDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcHJveHlba2V5XTtcbiAgICB9KTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZVRSUENDbGllbnQob3B0cykge1xuICAgIGNvbnN0IGNsaWVudCA9IG5ldyBUUlBDVW50eXBlZENsaWVudChvcHRzKTtcbiAgICBjb25zdCBwcm94eSA9IGNyZWF0ZVRSUENDbGllbnRQcm94eShjbGllbnQpO1xuICAgIHJldHVybiBwcm94eTtcbn1cbi8qKlxuICogR2V0IGFuIHVudHlwZWQgY2xpZW50IGZyb20gYSBwcm94eSBjbGllbnRcbiAqIEBpbnRlcm5hbFxuICovIGZ1bmN0aW9uIGdldFVudHlwZWRDbGllbnQoY2xpZW50KSB7XG4gICAgcmV0dXJuIGNsaWVudFt1bnR5cGVkQ2xpZW50U3ltYm9sXTtcbn1cblxuZXhwb3J0IHsgY2xpZW50Q2FsbFR5cGVUb1Byb2NlZHVyZVR5cGUsIGNyZWF0ZVRSUENDbGllbnQsIGNyZWF0ZVRSUENDbGllbnRQcm94eSwgZ2V0VW50eXBlZENsaWVudCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/createTRPCClient.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/createTRPCUntypedClient.mjs":
/*!********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/createTRPCUntypedClient.mjs ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TRPCUntypedClient: () => (/* reexport safe */ _internals_TRPCUntypedClient_mjs__WEBPACK_IMPORTED_MODULE_0__.TRPCUntypedClient),\n/* harmony export */   createTRPCUntypedClient: () => (/* binding */ createTRPCUntypedClient)\n/* harmony export */ });\n/* harmony import */ var _internals_TRPCUntypedClient_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internals/TRPCUntypedClient.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/TRPCUntypedClient.mjs\");\n\n\nfunction createTRPCUntypedClient(opts) {\n    return new _internals_TRPCUntypedClient_mjs__WEBPACK_IMPORTED_MODULE_0__.TRPCUntypedClient(opts);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNC9ub2RlX21vZHVsZXMvQHRycGMvY2xpZW50L2Rpc3QvY3JlYXRlVFJQQ1VudHlwZWRDbGllbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzRTs7QUFFdEU7QUFDQSxlQUFlLCtFQUFpQjtBQUNoQzs7QUFFc0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUiAxXFxEZXNrdG9wXFxkZXZvdHNwLXYxLjVcXHBvcnRhbHdpemFyZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHRycGMrY2xpZW50QDExLjEuMl9AdHJwYytzX2ZjOTY0M2NjYTBjODhmZDQ3MWNiNjY0MjBjZjIyZDM0XFxub2RlX21vZHVsZXNcXEB0cnBjXFxjbGllbnRcXGRpc3RcXGNyZWF0ZVRSUENVbnR5cGVkQ2xpZW50Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUUlBDVW50eXBlZENsaWVudCB9IGZyb20gJy4vaW50ZXJuYWxzL1RSUENVbnR5cGVkQ2xpZW50Lm1qcyc7XG5cbmZ1bmN0aW9uIGNyZWF0ZVRSUENVbnR5cGVkQ2xpZW50KG9wdHMpIHtcbiAgICByZXR1cm4gbmV3IFRSUENVbnR5cGVkQ2xpZW50KG9wdHMpO1xufVxuXG5leHBvcnQgeyBUUlBDVW50eXBlZENsaWVudCwgY3JlYXRlVFJQQ1VudHlwZWRDbGllbnQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/createTRPCUntypedClient.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/getFetch.mjs":
/*!*****************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/getFetch.mjs ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFetch: () => (/* binding */ getFetch)\n/* harmony export */ });\nconst isFunction = (fn)=>typeof fn === 'function';\nfunction getFetch(customFetchImpl) {\n    if (customFetchImpl) {\n        return customFetchImpl;\n    }\n    if (typeof window !== 'undefined' && isFunction(window.fetch)) {\n        return window.fetch;\n    }\n    if (typeof globalThis !== 'undefined' && isFunction(globalThis.fetch)) {\n        return globalThis.fetch;\n    }\n    throw new Error('No fetch implementation found');\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNC9ub2RlX21vZHVsZXMvQHRycGMvY2xpZW50L2Rpc3QvZ2V0RmV0Y2gubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFb0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUiAxXFxEZXNrdG9wXFxkZXZvdHNwLXYxLjVcXHBvcnRhbHdpemFyZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHRycGMrY2xpZW50QDExLjEuMl9AdHJwYytzX2ZjOTY0M2NjYTBjODhmZDQ3MWNiNjY0MjBjZjIyZDM0XFxub2RlX21vZHVsZXNcXEB0cnBjXFxjbGllbnRcXGRpc3RcXGdldEZldGNoLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc0Z1bmN0aW9uID0gKGZuKT0+dHlwZW9mIGZuID09PSAnZnVuY3Rpb24nO1xuZnVuY3Rpb24gZ2V0RmV0Y2goY3VzdG9tRmV0Y2hJbXBsKSB7XG4gICAgaWYgKGN1c3RvbUZldGNoSW1wbCkge1xuICAgICAgICByZXR1cm4gY3VzdG9tRmV0Y2hJbXBsO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgaXNGdW5jdGlvbih3aW5kb3cuZmV0Y2gpKSB7XG4gICAgICAgIHJldHVybiB3aW5kb3cuZmV0Y2g7XG4gICAgfVxuICAgIGlmICh0eXBlb2YgZ2xvYmFsVGhpcyAhPT0gJ3VuZGVmaW5lZCcgJiYgaXNGdW5jdGlvbihnbG9iYWxUaGlzLmZldGNoKSkge1xuICAgICAgICByZXR1cm4gZ2xvYmFsVGhpcy5mZXRjaDtcbiAgICB9XG4gICAgdGhyb3cgbmV3IEVycm9yKCdObyBmZXRjaCBpbXBsZW1lbnRhdGlvbiBmb3VuZCcpO1xufVxuXG5leHBvcnQgeyBnZXRGZXRjaCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/getFetch.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/index.mjs":
/*!**************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/index.mjs ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TRPCClientError: () => (/* reexport safe */ _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_3__.TRPCClientError),\n/* harmony export */   TRPCUntypedClient: () => (/* reexport safe */ _internals_TRPCUntypedClient_mjs__WEBPACK_IMPORTED_MODULE_13__.TRPCUntypedClient),\n/* harmony export */   clientCallTypeToProcedureType: () => (/* reexport safe */ _createTRPCClient_mjs__WEBPACK_IMPORTED_MODULE_1__.clientCallTypeToProcedureType),\n/* harmony export */   createTRPCClient: () => (/* reexport safe */ _createTRPCClient_mjs__WEBPACK_IMPORTED_MODULE_1__.createTRPCClient),\n/* harmony export */   createTRPCClientProxy: () => (/* reexport safe */ _createTRPCClient_mjs__WEBPACK_IMPORTED_MODULE_1__.createTRPCClientProxy),\n/* harmony export */   createTRPCProxyClient: () => (/* reexport safe */ _createTRPCClient_mjs__WEBPACK_IMPORTED_MODULE_1__.createTRPCClient),\n/* harmony export */   createTRPCUntypedClient: () => (/* reexport safe */ _createTRPCUntypedClient_mjs__WEBPACK_IMPORTED_MODULE_0__.createTRPCUntypedClient),\n/* harmony export */   createWSClient: () => (/* reexport safe */ _links_wsLink_createWsClient_mjs__WEBPACK_IMPORTED_MODULE_14__.createWSClient),\n/* harmony export */   getFetch: () => (/* reexport safe */ _getFetch_mjs__WEBPACK_IMPORTED_MODULE_2__.getFetch),\n/* harmony export */   getUntypedClient: () => (/* reexport safe */ _createTRPCClient_mjs__WEBPACK_IMPORTED_MODULE_1__.getUntypedClient),\n/* harmony export */   httpBatchLink: () => (/* reexport safe */ _links_httpBatchLink_mjs__WEBPACK_IMPORTED_MODULE_5__.httpBatchLink),\n/* harmony export */   httpBatchStreamLink: () => (/* reexport safe */ _links_httpBatchStreamLink_mjs__WEBPACK_IMPORTED_MODULE_6__.httpBatchStreamLink),\n/* harmony export */   httpLink: () => (/* reexport safe */ _links_httpLink_mjs__WEBPACK_IMPORTED_MODULE_7__.httpLink),\n/* harmony export */   httpSubscriptionLink: () => (/* reexport safe */ _links_httpSubscriptionLink_mjs__WEBPACK_IMPORTED_MODULE_11__.httpSubscriptionLink),\n/* harmony export */   isFormData: () => (/* reexport safe */ _links_internals_contentTypes_mjs__WEBPACK_IMPORTED_MODULE_4__.isFormData),\n/* harmony export */   isNonJsonSerializable: () => (/* reexport safe */ _links_internals_contentTypes_mjs__WEBPACK_IMPORTED_MODULE_4__.isNonJsonSerializable),\n/* harmony export */   isOctetType: () => (/* reexport safe */ _links_internals_contentTypes_mjs__WEBPACK_IMPORTED_MODULE_4__.isOctetType),\n/* harmony export */   loggerLink: () => (/* reexport safe */ _links_loggerLink_mjs__WEBPACK_IMPORTED_MODULE_8__.loggerLink),\n/* harmony export */   retryLink: () => (/* reexport safe */ _links_retryLink_mjs__WEBPACK_IMPORTED_MODULE_12__.retryLink),\n/* harmony export */   splitLink: () => (/* reexport safe */ _links_splitLink_mjs__WEBPACK_IMPORTED_MODULE_9__.splitLink),\n/* harmony export */   unstable_httpBatchStreamLink: () => (/* reexport safe */ _links_httpBatchStreamLink_mjs__WEBPACK_IMPORTED_MODULE_6__.unstable_httpBatchStreamLink),\n/* harmony export */   unstable_httpSubscriptionLink: () => (/* reexport safe */ _links_httpSubscriptionLink_mjs__WEBPACK_IMPORTED_MODULE_11__.unstable_httpSubscriptionLink),\n/* harmony export */   wsLink: () => (/* reexport safe */ _links_wsLink_wsLink_mjs__WEBPACK_IMPORTED_MODULE_10__.wsLink)\n/* harmony export */ });\n/* harmony import */ var _createTRPCUntypedClient_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createTRPCUntypedClient.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/createTRPCUntypedClient.mjs\");\n/* harmony import */ var _createTRPCClient_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createTRPCClient.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/createTRPCClient.mjs\");\n/* harmony import */ var _getFetch_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getFetch.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/getFetch.mjs\");\n/* harmony import */ var _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TRPCClientError.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/TRPCClientError.mjs\");\n/* harmony import */ var _links_internals_contentTypes_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./links/internals/contentTypes.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/contentTypes.mjs\");\n/* harmony import */ var _links_httpBatchLink_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./links/httpBatchLink.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpBatchLink.mjs\");\n/* harmony import */ var _links_httpBatchStreamLink_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./links/httpBatchStreamLink.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpBatchStreamLink.mjs\");\n/* harmony import */ var _links_httpLink_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./links/httpLink.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpLink.mjs\");\n/* harmony import */ var _links_loggerLink_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./links/loggerLink.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/loggerLink.mjs\");\n/* harmony import */ var _links_splitLink_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./links/splitLink.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/splitLink.mjs\");\n/* harmony import */ var _links_wsLink_wsLink_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./links/wsLink/wsLink.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsLink.mjs\");\n/* harmony import */ var _links_httpSubscriptionLink_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./links/httpSubscriptionLink.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpSubscriptionLink.mjs\");\n/* harmony import */ var _links_retryLink_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./links/retryLink.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/retryLink.mjs\");\n/* harmony import */ var _internals_TRPCUntypedClient_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./internals/TRPCUntypedClient.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/TRPCUntypedClient.mjs\");\n/* harmony import */ var _links_wsLink_createWsClient_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./links/wsLink/createWsClient.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/createWsClient.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/TRPCUntypedClient.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/TRPCUntypedClient.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TRPCUntypedClient: () => (/* binding */ TRPCUntypedClient)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/observable */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/observable/index.mjs\");\n/* harmony import */ var _links_internals_createChain_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../links/internals/createChain.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/createChain.mjs\");\n/* harmony import */ var _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../TRPCClientError.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/TRPCClientError.mjs\");\n\n\n\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nclass TRPCUntypedClient {\n    $request(opts) {\n        const chain$ = (0,_links_internals_createChain_mjs__WEBPACK_IMPORTED_MODULE_1__.createChain)({\n            links: this.links,\n            op: {\n                ...opts,\n                context: opts.context ?? {},\n                id: ++this.requestId\n            }\n        });\n        return chain$.pipe((0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.share)());\n    }\n    async requestAsPromise(opts) {\n        try {\n            const req$ = this.$request(opts);\n            const envelope = await (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.observableToPromise)(req$);\n            const data = envelope.result.data;\n            return data;\n        } catch (err) {\n            throw _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_2__.TRPCClientError.from(err);\n        }\n    }\n    query(path, input, opts) {\n        return this.requestAsPromise({\n            type: 'query',\n            path,\n            input,\n            context: opts?.context,\n            signal: opts?.signal\n        });\n    }\n    mutation(path, input, opts) {\n        return this.requestAsPromise({\n            type: 'mutation',\n            path,\n            input,\n            context: opts?.context,\n            signal: opts?.signal\n        });\n    }\n    subscription(path, input, opts) {\n        const observable$ = this.$request({\n            type: 'subscription',\n            path,\n            input,\n            context: opts.context,\n            signal: opts.signal\n        });\n        return observable$.subscribe({\n            next (envelope) {\n                switch(envelope.result.type){\n                    case 'state':\n                        {\n                            opts.onConnectionStateChange?.(envelope.result);\n                            break;\n                        }\n                    case 'started':\n                        {\n                            opts.onStarted?.({\n                                context: envelope.context\n                            });\n                            break;\n                        }\n                    case 'stopped':\n                        {\n                            opts.onStopped?.();\n                            break;\n                        }\n                    case 'data':\n                    case undefined:\n                        {\n                            opts.onData?.(envelope.result.data);\n                            break;\n                        }\n                }\n            },\n            error (err) {\n                opts.onError?.(err);\n            },\n            complete () {\n                opts.onComplete?.();\n            }\n        });\n    }\n    constructor(opts){\n        _define_property(this, \"links\", void 0);\n        _define_property(this, \"runtime\", void 0);\n        _define_property(this, \"requestId\", void 0);\n        this.requestId = 0;\n        this.runtime = {};\n        // Initialize the links\n        this.links = opts.links.map((link)=>link(this.runtime));\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/TRPCUntypedClient.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/dataLoader.mjs":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/dataLoader.mjs ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dataLoader: () => (/* binding */ dataLoader)\n/* harmony export */ });\n/* eslint-disable @typescript-eslint/no-non-null-assertion */ /**\n * A function that should never be called unless we messed something up.\n */ const throwFatalError = ()=>{\n    throw new Error('Something went wrong. Please submit an issue at https://github.com/trpc/trpc/issues/new');\n};\n/**\n * Dataloader that's very inspired by https://github.com/graphql/dataloader\n * Less configuration, no caching, and allows you to cancel requests\n * When cancelling a single fetch the whole batch will be cancelled only when _all_ items are cancelled\n */ function dataLoader(batchLoader) {\n    let pendingItems = null;\n    let dispatchTimer = null;\n    const destroyTimerAndPendingItems = ()=>{\n        clearTimeout(dispatchTimer);\n        dispatchTimer = null;\n        pendingItems = null;\n    };\n    /**\n   * Iterate through the items and split them into groups based on the `batchLoader`'s validate function\n   */ function groupItems(items) {\n        const groupedItems = [\n            []\n        ];\n        let index = 0;\n        while(true){\n            const item = items[index];\n            if (!item) {\n                break;\n            }\n            const lastGroup = groupedItems[groupedItems.length - 1];\n            if (item.aborted) {\n                // Item was aborted before it was dispatched\n                item.reject?.(new Error('Aborted'));\n                index++;\n                continue;\n            }\n            const isValid = batchLoader.validate(lastGroup.concat(item).map((it)=>it.key));\n            if (isValid) {\n                lastGroup.push(item);\n                index++;\n                continue;\n            }\n            if (lastGroup.length === 0) {\n                item.reject?.(new Error('Input is too big for a single dispatch'));\n                index++;\n                continue;\n            }\n            // Create new group, next iteration will try to add the item to that\n            groupedItems.push([]);\n        }\n        return groupedItems;\n    }\n    function dispatch() {\n        const groupedItems = groupItems(pendingItems);\n        destroyTimerAndPendingItems();\n        // Create batches for each group of items\n        for (const items of groupedItems){\n            if (!items.length) {\n                continue;\n            }\n            const batch = {\n                items\n            };\n            for (const item of items){\n                item.batch = batch;\n            }\n            const promise = batchLoader.fetch(batch.items.map((_item)=>_item.key));\n            promise.then(async (result)=>{\n                await Promise.all(result.map(async (valueOrPromise, index)=>{\n                    const item = batch.items[index];\n                    try {\n                        const value = await Promise.resolve(valueOrPromise);\n                        item.resolve?.(value);\n                    } catch (cause) {\n                        item.reject?.(cause);\n                    }\n                    item.batch = null;\n                    item.reject = null;\n                    item.resolve = null;\n                }));\n                for (const item of batch.items){\n                    item.reject?.(new Error('Missing result'));\n                    item.batch = null;\n                }\n            }).catch((cause)=>{\n                for (const item of batch.items){\n                    item.reject?.(cause);\n                    item.batch = null;\n                }\n            });\n        }\n    }\n    function load(key) {\n        const item = {\n            aborted: false,\n            key,\n            batch: null,\n            resolve: throwFatalError,\n            reject: throwFatalError\n        };\n        const promise = new Promise((resolve, reject)=>{\n            item.reject = reject;\n            item.resolve = resolve;\n            if (!pendingItems) {\n                pendingItems = [];\n            }\n            pendingItems.push(item);\n        });\n        if (!dispatchTimer) {\n            dispatchTimer = setTimeout(dispatch);\n        }\n        return promise;\n    }\n    return {\n        load\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/dataLoader.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/inputWithTrackedEventId.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/inputWithTrackedEventId.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inputWithTrackedEventId: () => (/* binding */ inputWithTrackedEventId)\n/* harmony export */ });\nfunction inputWithTrackedEventId(input, lastEventId) {\n    if (!lastEventId) {\n        return input;\n    }\n    if (input != null && typeof input !== 'object') {\n        return input;\n    }\n    return {\n        ...input ?? {},\n        lastEventId\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNC9ub2RlX21vZHVsZXMvQHRycGMvY2xpZW50L2Rpc3QvaW50ZXJuYWxzL2lucHV0V2l0aFRyYWNrZWRFdmVudElkLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7O0FBRW1DIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVIgMVxcRGVza3RvcFxcZGV2b3RzcC12MS41XFxwb3J0YWx3aXphcmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEB0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNFxcbm9kZV9tb2R1bGVzXFxAdHJwY1xcY2xpZW50XFxkaXN0XFxpbnRlcm5hbHNcXGlucHV0V2l0aFRyYWNrZWRFdmVudElkLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBpbnB1dFdpdGhUcmFja2VkRXZlbnRJZChpbnB1dCwgbGFzdEV2ZW50SWQpIHtcbiAgICBpZiAoIWxhc3RFdmVudElkKSB7XG4gICAgICAgIHJldHVybiBpbnB1dDtcbiAgICB9XG4gICAgaWYgKGlucHV0ICE9IG51bGwgJiYgdHlwZW9mIGlucHV0ICE9PSAnb2JqZWN0Jykge1xuICAgICAgICByZXR1cm4gaW5wdXQ7XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICAgIC4uLmlucHV0ID8/IHt9LFxuICAgICAgICBsYXN0RXZlbnRJZFxuICAgIH07XG59XG5cbmV4cG9ydCB7IGlucHV0V2l0aFRyYWNrZWRFdmVudElkIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/inputWithTrackedEventId.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/signals.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/signals.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allAbortSignals: () => (/* binding */ allAbortSignals),\n/* harmony export */   raceAbortSignals: () => (/* binding */ raceAbortSignals)\n/* harmony export */ });\n/**\n * Like `Promise.all()` but for abort signals\n * - When all signals have been aborted, the merged signal will be aborted\n * - If one signal is `null`, no signal will be aborted\n */ function allAbortSignals(...signals) {\n    const ac = new AbortController();\n    const count = signals.length;\n    let abortedCount = 0;\n    const onAbort = ()=>{\n        if (++abortedCount === count) {\n            ac.abort();\n        }\n    };\n    for (const signal of signals){\n        if (signal?.aborted) {\n            onAbort();\n        } else {\n            signal?.addEventListener('abort', onAbort, {\n                once: true\n            });\n        }\n    }\n    return ac.signal;\n}\n/**\n * Like `Promise.race` but for abort signals\n *\n * Basically, a ponyfill for\n * [`AbortSignal.any`](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/any_static).\n */ function raceAbortSignals(...signals) {\n    const ac = new AbortController();\n    for (const signal of signals){\n        if (signal?.aborted) {\n            ac.abort();\n        } else {\n            signal?.addEventListener('abort', ()=>ac.abort(), {\n                once: true\n            });\n        }\n    }\n    return ac.signal;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/signals.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/transformer.mjs":
/*!******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/transformer.mjs ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTransformer: () => (/* binding */ getTransformer)\n/* harmony export */ });\n/**\n * @internal\n */ /**\n * @internal\n */ function getTransformer(transformer) {\n    const _transformer = transformer;\n    if (!_transformer) {\n        return {\n            input: {\n                serialize: (data)=>data,\n                deserialize: (data)=>data\n            },\n            output: {\n                serialize: (data)=>data,\n                deserialize: (data)=>data\n            }\n        };\n    }\n    if ('input' in _transformer) {\n        return _transformer;\n    }\n    return {\n        input: _transformer,\n        output: _transformer\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNC9ub2RlX21vZHVsZXMvQHRycGMvY2xpZW50L2Rpc3QvaW50ZXJuYWxzL3RyYW5zZm9ybWVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUwQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSIDFcXERlc2t0b3BcXGRldm90c3AtdjEuNVxccG9ydGFsd2l6YXJkXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAdHJwYytjbGllbnRAMTEuMS4yX0B0cnBjK3NfZmM5NjQzY2NhMGM4OGZkNDcxY2I2NjQyMGNmMjJkMzRcXG5vZGVfbW9kdWxlc1xcQHRycGNcXGNsaWVudFxcZGlzdFxcaW50ZXJuYWxzXFx0cmFuc2Zvcm1lci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW50ZXJuYWxcbiAqLyAvKipcbiAqIEBpbnRlcm5hbFxuICovIGZ1bmN0aW9uIGdldFRyYW5zZm9ybWVyKHRyYW5zZm9ybWVyKSB7XG4gICAgY29uc3QgX3RyYW5zZm9ybWVyID0gdHJhbnNmb3JtZXI7XG4gICAgaWYgKCFfdHJhbnNmb3JtZXIpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGlucHV0OiB7XG4gICAgICAgICAgICAgICAgc2VyaWFsaXplOiAoZGF0YSk9PmRhdGEsXG4gICAgICAgICAgICAgICAgZGVzZXJpYWxpemU6IChkYXRhKT0+ZGF0YVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIG91dHB1dDoge1xuICAgICAgICAgICAgICAgIHNlcmlhbGl6ZTogKGRhdGEpPT5kYXRhLFxuICAgICAgICAgICAgICAgIGRlc2VyaWFsaXplOiAoZGF0YSk9PmRhdGFcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICB9XG4gICAgaWYgKCdpbnB1dCcgaW4gX3RyYW5zZm9ybWVyKSB7XG4gICAgICAgIHJldHVybiBfdHJhbnNmb3JtZXI7XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICAgIGlucHV0OiBfdHJhbnNmb3JtZXIsXG4gICAgICAgIG91dHB1dDogX3RyYW5zZm9ybWVyXG4gICAgfTtcbn1cblxuZXhwb3J0IHsgZ2V0VHJhbnNmb3JtZXIgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/transformer.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpBatchLink.mjs":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpBatchLink.mjs ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   httpBatchLink: () => (/* binding */ httpBatchLink)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/observable */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/observable/index.mjs\");\n/* harmony import */ var _trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @trpc/server/unstable-core-do-not-import */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/unstable-core-do-not-import.mjs\");\n/* harmony import */ var _internals_dataLoader_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../internals/dataLoader.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/dataLoader.mjs\");\n/* harmony import */ var _internals_signals_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../internals/signals.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/signals.mjs\");\n/* harmony import */ var _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../TRPCClientError.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/TRPCClientError.mjs\");\n/* harmony import */ var _internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./internals/httpUtils.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/httpUtils.mjs\");\n\n\n\n\n\n\n\n/**\n * @see https://trpc.io/docs/client/links/httpBatchLink\n */ function httpBatchLink(opts) {\n    const resolvedOpts = (0,_internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_5__.resolveHTTPLinkOptions)(opts);\n    const maxURLLength = opts.maxURLLength ?? Infinity;\n    const maxItems = opts.maxItems ?? Infinity;\n    return ()=>{\n        const batchLoader = (type)=>{\n            return {\n                validate (batchOps) {\n                    if (maxURLLength === Infinity && maxItems === Infinity) {\n                        // escape hatch for quick calcs\n                        return true;\n                    }\n                    if (batchOps.length > maxItems) {\n                        return false;\n                    }\n                    const path = batchOps.map((op)=>op.path).join(',');\n                    const inputs = batchOps.map((op)=>op.input);\n                    const url = (0,_internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_5__.getUrl)({\n                        ...resolvedOpts,\n                        type,\n                        path,\n                        inputs,\n                        signal: null\n                    });\n                    return url.length <= maxURLLength;\n                },\n                async fetch (batchOps) {\n                    const path = batchOps.map((op)=>op.path).join(',');\n                    const inputs = batchOps.map((op)=>op.input);\n                    const signal = (0,_internals_signals_mjs__WEBPACK_IMPORTED_MODULE_3__.allAbortSignals)(...batchOps.map((op)=>op.signal));\n                    const res = await (0,_internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_5__.jsonHttpRequester)({\n                        ...resolvedOpts,\n                        path,\n                        inputs,\n                        type,\n                        headers () {\n                            if (!opts.headers) {\n                                return {};\n                            }\n                            if (typeof opts.headers === 'function') {\n                                return opts.headers({\n                                    opList: batchOps\n                                });\n                            }\n                            return opts.headers;\n                        },\n                        signal\n                    });\n                    const resJSON = Array.isArray(res.json) ? res.json : batchOps.map(()=>res.json);\n                    const result = resJSON.map((item)=>({\n                            meta: res.meta,\n                            json: item\n                        }));\n                    return result;\n                }\n            };\n        };\n        const query = (0,_internals_dataLoader_mjs__WEBPACK_IMPORTED_MODULE_2__.dataLoader)(batchLoader('query'));\n        const mutation = (0,_internals_dataLoader_mjs__WEBPACK_IMPORTED_MODULE_2__.dataLoader)(batchLoader('mutation'));\n        const loaders = {\n            query,\n            mutation\n        };\n        return ({ op })=>{\n            return (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.observable)((observer)=>{\n                /* istanbul ignore if -- @preserve */ if (op.type === 'subscription') {\n                    throw new Error('Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`');\n                }\n                const loader = loaders[op.type];\n                const promise = loader.load(op);\n                let _res = undefined;\n                promise.then((res)=>{\n                    _res = res;\n                    const transformed = (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_1__.transformResult)(res.json, resolvedOpts.transformer.output);\n                    if (!transformed.ok) {\n                        observer.error(_TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_4__.TRPCClientError.from(transformed.error, {\n                            meta: res.meta\n                        }));\n                        return;\n                    }\n                    observer.next({\n                        context: res.meta,\n                        result: transformed.result\n                    });\n                    observer.complete();\n                }).catch((err)=>{\n                    observer.error(_TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_4__.TRPCClientError.from(err, {\n                        meta: _res?.meta\n                    }));\n                });\n                return ()=>{\n                // noop\n                };\n            });\n        };\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpBatchLink.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpBatchStreamLink.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpBatchStreamLink.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   httpBatchStreamLink: () => (/* binding */ httpBatchStreamLink),\n/* harmony export */   unstable_httpBatchStreamLink: () => (/* binding */ unstable_httpBatchStreamLink)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/observable */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/observable/index.mjs\");\n/* harmony import */ var _trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @trpc/server/unstable-core-do-not-import */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/unstable-core-do-not-import.mjs\");\n/* harmony import */ var _internals_dataLoader_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../internals/dataLoader.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/dataLoader.mjs\");\n/* harmony import */ var _internals_signals_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../internals/signals.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/signals.mjs\");\n/* harmony import */ var _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../TRPCClientError.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/TRPCClientError.mjs\");\n/* harmony import */ var _internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./internals/httpUtils.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/httpUtils.mjs\");\n\n\n\n\n\n\n\n/**\n * @see https://trpc.io/docs/client/links/httpBatchStreamLink\n */ function httpBatchStreamLink(opts) {\n    const resolvedOpts = (0,_internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_5__.resolveHTTPLinkOptions)(opts);\n    const maxURLLength = opts.maxURLLength ?? Infinity;\n    const maxItems = opts.maxItems ?? Infinity;\n    return ()=>{\n        const batchLoader = (type)=>{\n            return {\n                validate (batchOps) {\n                    if (maxURLLength === Infinity && maxItems === Infinity) {\n                        // escape hatch for quick calcs\n                        return true;\n                    }\n                    if (batchOps.length > maxItems) {\n                        return false;\n                    }\n                    const path = batchOps.map((op)=>op.path).join(',');\n                    const inputs = batchOps.map((op)=>op.input);\n                    const url = (0,_internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_5__.getUrl)({\n                        ...resolvedOpts,\n                        type,\n                        path,\n                        inputs,\n                        signal: null\n                    });\n                    return url.length <= maxURLLength;\n                },\n                async fetch (batchOps) {\n                    const path = batchOps.map((op)=>op.path).join(',');\n                    const inputs = batchOps.map((op)=>op.input);\n                    const batchSignals = (0,_internals_signals_mjs__WEBPACK_IMPORTED_MODULE_3__.allAbortSignals)(...batchOps.map((op)=>op.signal));\n                    const abortController = new AbortController();\n                    const responsePromise = (0,_internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_5__.fetchHTTPResponse)({\n                        ...resolvedOpts,\n                        signal: (0,_internals_signals_mjs__WEBPACK_IMPORTED_MODULE_3__.raceAbortSignals)(batchSignals, abortController.signal),\n                        type,\n                        contentTypeHeader: 'application/json',\n                        trpcAcceptHeader: 'application/jsonl',\n                        getUrl: _internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_5__.getUrl,\n                        getBody: _internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_5__.getBody,\n                        inputs,\n                        path,\n                        headers () {\n                            if (!opts.headers) {\n                                return {};\n                            }\n                            if (typeof opts.headers === 'function') {\n                                return opts.headers({\n                                    opList: batchOps\n                                });\n                            }\n                            return opts.headers;\n                        }\n                    });\n                    const res = await responsePromise;\n                    const [head] = await (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_1__.jsonlStreamConsumer)({\n                        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                        from: res.body,\n                        deserialize: resolvedOpts.transformer.output.deserialize,\n                        // onError: console.error,\n                        formatError (opts) {\n                            const error = opts.error;\n                            return _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_4__.TRPCClientError.from({\n                                error\n                            });\n                        },\n                        abortController\n                    });\n                    const promises = Object.keys(batchOps).map(async (key)=>{\n                        let json = await Promise.resolve(head[key]);\n                        if ('result' in json) {\n                            /**\n                 * Not very pretty, but we need to unwrap nested data as promises\n                 * Our stream producer will only resolve top-level async values or async values that are directly nested in another async value\n                 */ const result = await Promise.resolve(json.result);\n                            json = {\n                                result: {\n                                    data: await Promise.resolve(result.data)\n                                }\n                            };\n                        }\n                        return {\n                            json,\n                            meta: {\n                                response: res\n                            }\n                        };\n                    });\n                    return promises;\n                }\n            };\n        };\n        const query = (0,_internals_dataLoader_mjs__WEBPACK_IMPORTED_MODULE_2__.dataLoader)(batchLoader('query'));\n        const mutation = (0,_internals_dataLoader_mjs__WEBPACK_IMPORTED_MODULE_2__.dataLoader)(batchLoader('mutation'));\n        const loaders = {\n            query,\n            mutation\n        };\n        return ({ op })=>{\n            return (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.observable)((observer)=>{\n                /* istanbul ignore if -- @preserve */ if (op.type === 'subscription') {\n                    throw new Error('Subscriptions are unsupported by `httpBatchStreamLink` - use `httpSubscriptionLink` or `wsLink`');\n                }\n                const loader = loaders[op.type];\n                const promise = loader.load(op);\n                let _res = undefined;\n                promise.then((res)=>{\n                    _res = res;\n                    if ('error' in res.json) {\n                        observer.error(_TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_4__.TRPCClientError.from(res.json, {\n                            meta: res.meta\n                        }));\n                        return;\n                    } else if ('result' in res.json) {\n                        observer.next({\n                            context: res.meta,\n                            result: res.json.result\n                        });\n                        observer.complete();\n                        return;\n                    }\n                    observer.complete();\n                }).catch((err)=>{\n                    observer.error(_TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_4__.TRPCClientError.from(err, {\n                        meta: _res?.meta\n                    }));\n                });\n                return ()=>{\n                // noop\n                };\n            });\n        };\n    };\n}\n/**\n * @deprecated use {@link httpBatchStreamLink} instead\n */ const unstable_httpBatchStreamLink = httpBatchStreamLink;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpBatchStreamLink.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpLink.mjs":
/*!***********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpLink.mjs ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   httpLink: () => (/* binding */ httpLink)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/observable */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/observable/index.mjs\");\n/* harmony import */ var _trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @trpc/server/unstable-core-do-not-import */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/unstable-core-do-not-import.mjs\");\n/* harmony import */ var _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../TRPCClientError.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/TRPCClientError.mjs\");\n/* harmony import */ var _internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./internals/httpUtils.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/httpUtils.mjs\");\n/* harmony import */ var _internals_contentTypes_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./internals/contentTypes.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/contentTypes.mjs\");\n\n\n\n\n\n\nconst universalRequester = (opts)=>{\n    if ('input' in opts) {\n        const { input } = opts;\n        if ((0,_internals_contentTypes_mjs__WEBPACK_IMPORTED_MODULE_4__.isFormData)(input)) {\n            if (opts.type !== 'mutation' && opts.methodOverride !== 'POST') {\n                throw new Error('FormData is only supported for mutations');\n            }\n            return (0,_internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_3__.httpRequest)({\n                ...opts,\n                // The browser will set this automatically and include the boundary= in it\n                contentTypeHeader: undefined,\n                getUrl: _internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_3__.getUrl,\n                getBody: ()=>input\n            });\n        }\n        if ((0,_internals_contentTypes_mjs__WEBPACK_IMPORTED_MODULE_4__.isOctetType)(input)) {\n            if (opts.type !== 'mutation' && opts.methodOverride !== 'POST') {\n                throw new Error('Octet type input is only supported for mutations');\n            }\n            return (0,_internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_3__.httpRequest)({\n                ...opts,\n                contentTypeHeader: 'application/octet-stream',\n                getUrl: _internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_3__.getUrl,\n                getBody: ()=>input\n            });\n        }\n    }\n    return (0,_internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_3__.jsonHttpRequester)(opts);\n};\n/**\n * @see https://trpc.io/docs/client/links/httpLink\n */ function httpLink(opts) {\n    const resolvedOpts = (0,_internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_3__.resolveHTTPLinkOptions)(opts);\n    return ()=>{\n        return ({ op })=>{\n            return (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.observable)((observer)=>{\n                const { path, input, type } = op;\n                /* istanbul ignore if -- @preserve */ if (type === 'subscription') {\n                    throw new Error('Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`');\n                }\n                const request = universalRequester({\n                    ...resolvedOpts,\n                    type,\n                    path,\n                    input,\n                    signal: op.signal,\n                    headers () {\n                        if (!opts.headers) {\n                            return {};\n                        }\n                        if (typeof opts.headers === 'function') {\n                            return opts.headers({\n                                op\n                            });\n                        }\n                        return opts.headers;\n                    }\n                });\n                let meta = undefined;\n                request.then((res)=>{\n                    meta = res.meta;\n                    const transformed = (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_1__.transformResult)(res.json, resolvedOpts.transformer.output);\n                    if (!transformed.ok) {\n                        observer.error(_TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_2__.TRPCClientError.from(transformed.error, {\n                            meta\n                        }));\n                        return;\n                    }\n                    observer.next({\n                        context: res.meta,\n                        result: transformed.result\n                    });\n                    observer.complete();\n                }).catch((cause)=>{\n                    observer.error(_TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_2__.TRPCClientError.from(cause, {\n                        meta\n                    }));\n                });\n                return ()=>{\n                // noop\n                };\n            });\n        };\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpLink.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpSubscriptionLink.mjs":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpSubscriptionLink.mjs ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   httpSubscriptionLink: () => (/* binding */ httpSubscriptionLink),\n/* harmony export */   unstable_httpSubscriptionLink: () => (/* binding */ unstable_httpSubscriptionLink)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/observable */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/observable/index.mjs\");\n/* harmony import */ var _trpc_server_rpc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @trpc/server/rpc */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/rpc.mjs\");\n/* harmony import */ var _trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @trpc/server/unstable-core-do-not-import */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/unstable-core-do-not-import.mjs\");\n/* harmony import */ var _internals_inputWithTrackedEventId_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../internals/inputWithTrackedEventId.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/inputWithTrackedEventId.mjs\");\n/* harmony import */ var _internals_signals_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../internals/signals.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/signals.mjs\");\n/* harmony import */ var _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../TRPCClientError.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/TRPCClientError.mjs\");\n/* harmony import */ var _internals_transformer_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../internals/transformer.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/transformer.mjs\");\n/* harmony import */ var _internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./internals/httpUtils.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/httpUtils.mjs\");\n/* harmony import */ var _internals_urlWithConnectionParams_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./internals/urlWithConnectionParams.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/urlWithConnectionParams.mjs\");\n\n\n\n\n\n\n\n\n\n\nasync function urlWithConnectionParams(opts) {\n    let url = await (0,_internals_urlWithConnectionParams_mjs__WEBPACK_IMPORTED_MODULE_8__.resultOf)(opts.url);\n    if (opts.connectionParams) {\n        const params = await (0,_internals_urlWithConnectionParams_mjs__WEBPACK_IMPORTED_MODULE_8__.resultOf)(opts.connectionParams);\n        const prefix = url.includes('?') ? '&' : '?';\n        url += prefix + 'connectionParams=' + encodeURIComponent(JSON.stringify(params));\n    }\n    return url;\n}\n/**\n * tRPC error codes that are considered retryable\n * With out of the box SSE, the client will reconnect when these errors are encountered\n */ const codes5xx = [\n    _trpc_server_rpc__WEBPACK_IMPORTED_MODULE_1__.TRPC_ERROR_CODES_BY_KEY.BAD_GATEWAY,\n    _trpc_server_rpc__WEBPACK_IMPORTED_MODULE_1__.TRPC_ERROR_CODES_BY_KEY.SERVICE_UNAVAILABLE,\n    _trpc_server_rpc__WEBPACK_IMPORTED_MODULE_1__.TRPC_ERROR_CODES_BY_KEY.GATEWAY_TIMEOUT,\n    _trpc_server_rpc__WEBPACK_IMPORTED_MODULE_1__.TRPC_ERROR_CODES_BY_KEY.INTERNAL_SERVER_ERROR\n];\n/**\n * @see https://trpc.io/docs/client/links/httpSubscriptionLink\n */ function httpSubscriptionLink(opts) {\n    const transformer = (0,_internals_transformer_mjs__WEBPACK_IMPORTED_MODULE_6__.getTransformer)(opts.transformer);\n    return ()=>{\n        return ({ op })=>{\n            return (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.observable)((observer)=>{\n                const { type, path, input } = op;\n                /* istanbul ignore if -- @preserve */ if (type !== 'subscription') {\n                    throw new Error('httpSubscriptionLink only supports subscriptions');\n                }\n                let lastEventId = undefined;\n                const ac = new AbortController();\n                const signal = (0,_internals_signals_mjs__WEBPACK_IMPORTED_MODULE_4__.raceAbortSignals)(op.signal, ac.signal);\n                const eventSourceStream = (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_2__.sseStreamConsumer)({\n                    url: async ()=>(0,_internals_httpUtils_mjs__WEBPACK_IMPORTED_MODULE_7__.getUrl)({\n                            transformer,\n                            url: await urlWithConnectionParams(opts),\n                            input: (0,_internals_inputWithTrackedEventId_mjs__WEBPACK_IMPORTED_MODULE_3__.inputWithTrackedEventId)(input, lastEventId),\n                            path,\n                            type,\n                            signal: null\n                        }),\n                    init: ()=>(0,_internals_urlWithConnectionParams_mjs__WEBPACK_IMPORTED_MODULE_8__.resultOf)(opts.eventSourceOptions, {\n                            op\n                        }),\n                    signal,\n                    deserialize: transformer.output.deserialize,\n                    EventSource: opts.EventSource ?? globalThis.EventSource\n                });\n                const connectionState = (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.behaviorSubject)({\n                    type: 'state',\n                    state: 'connecting',\n                    error: null\n                });\n                const connectionSub = connectionState.subscribe({\n                    next (state) {\n                        observer.next({\n                            result: state\n                        });\n                    }\n                });\n                (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_2__.run)(async ()=>{\n                    for await (const chunk of eventSourceStream){\n                        switch(chunk.type){\n                            case 'ping':\n                                break;\n                            case 'data':\n                                const chunkData = chunk.data;\n                                let result;\n                                if (chunkData.id) {\n                                    // if the `tracked()`-helper is used, we always have an `id` field\n                                    lastEventId = chunkData.id;\n                                    result = {\n                                        id: chunkData.id,\n                                        data: chunkData\n                                    };\n                                } else {\n                                    result = {\n                                        data: chunkData.data\n                                    };\n                                }\n                                observer.next({\n                                    result,\n                                    context: {\n                                        eventSource: chunk.eventSource\n                                    }\n                                });\n                                break;\n                            case 'connected':\n                                {\n                                    observer.next({\n                                        result: {\n                                            type: 'started'\n                                        },\n                                        context: {\n                                            eventSource: chunk.eventSource\n                                        }\n                                    });\n                                    connectionState.next({\n                                        type: 'state',\n                                        state: 'pending',\n                                        error: null\n                                    });\n                                    break;\n                                }\n                            case 'serialized-error':\n                                {\n                                    const error = _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_5__.TRPCClientError.from({\n                                        error: chunk.error\n                                    });\n                                    if (codes5xx.includes(chunk.error.code)) {\n                                        //\n                                        connectionState.next({\n                                            type: 'state',\n                                            state: 'connecting',\n                                            error\n                                        });\n                                        break;\n                                    }\n                                    //\n                                    // non-retryable error, cancel the subscription\n                                    throw error;\n                                }\n                            case 'connecting':\n                                {\n                                    const lastState = connectionState.get();\n                                    const error = chunk.event && _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_5__.TRPCClientError.from(chunk.event);\n                                    if (!error && lastState.state === 'connecting') {\n                                        break;\n                                    }\n                                    connectionState.next({\n                                        type: 'state',\n                                        state: 'connecting',\n                                        error\n                                    });\n                                    break;\n                                }\n                            case 'timeout':\n                                {\n                                    connectionState.next({\n                                        type: 'state',\n                                        state: 'connecting',\n                                        error: new _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_5__.TRPCClientError(`Timeout of ${chunk.ms}ms reached while waiting for a response`)\n                                    });\n                                }\n                        }\n                    }\n                    observer.next({\n                        result: {\n                            type: 'stopped'\n                        }\n                    });\n                    connectionState.next({\n                        type: 'state',\n                        state: 'idle',\n                        error: null\n                    });\n                    observer.complete();\n                }).catch((error)=>{\n                    observer.error(_TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_5__.TRPCClientError.from(error));\n                });\n                return ()=>{\n                    observer.complete();\n                    ac.abort();\n                    connectionSub.unsubscribe();\n                };\n            });\n        };\n    };\n}\n/**\n * @deprecated use {@link httpSubscriptionLink} instead\n */ const unstable_httpSubscriptionLink = httpSubscriptionLink;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/httpSubscriptionLink.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/contentTypes.mjs":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/contentTypes.mjs ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isFormData: () => (/* binding */ isFormData),\n/* harmony export */   isNonJsonSerializable: () => (/* binding */ isNonJsonSerializable),\n/* harmony export */   isOctetType: () => (/* binding */ isOctetType)\n/* harmony export */ });\nfunction isOctetType(input) {\n    return input instanceof Uint8Array || // File extends from Blob but is only available in nodejs from v20\n    input instanceof Blob;\n}\nfunction isFormData(input) {\n    return input instanceof FormData;\n}\nfunction isNonJsonSerializable(input) {\n    return isOctetType(input) || isFormData(input);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNC9ub2RlX21vZHVsZXMvQHRycGMvY2xpZW50L2Rpc3QvbGlua3MvaW50ZXJuYWxzL2NvbnRlbnRUeXBlcy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTBEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVIgMVxcRGVza3RvcFxcZGV2b3RzcC12MS41XFxwb3J0YWx3aXphcmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEB0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNFxcbm9kZV9tb2R1bGVzXFxAdHJwY1xcY2xpZW50XFxkaXN0XFxsaW5rc1xcaW50ZXJuYWxzXFxjb250ZW50VHlwZXMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGlzT2N0ZXRUeXBlKGlucHV0KSB7XG4gICAgcmV0dXJuIGlucHV0IGluc3RhbmNlb2YgVWludDhBcnJheSB8fCAvLyBGaWxlIGV4dGVuZHMgZnJvbSBCbG9iIGJ1dCBpcyBvbmx5IGF2YWlsYWJsZSBpbiBub2RlanMgZnJvbSB2MjBcbiAgICBpbnB1dCBpbnN0YW5jZW9mIEJsb2I7XG59XG5mdW5jdGlvbiBpc0Zvcm1EYXRhKGlucHV0KSB7XG4gICAgcmV0dXJuIGlucHV0IGluc3RhbmNlb2YgRm9ybURhdGE7XG59XG5mdW5jdGlvbiBpc05vbkpzb25TZXJpYWxpemFibGUoaW5wdXQpIHtcbiAgICByZXR1cm4gaXNPY3RldFR5cGUoaW5wdXQpIHx8IGlzRm9ybURhdGEoaW5wdXQpO1xufVxuXG5leHBvcnQgeyBpc0Zvcm1EYXRhLCBpc05vbkpzb25TZXJpYWxpemFibGUsIGlzT2N0ZXRUeXBlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/contentTypes.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/createChain.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/createChain.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createChain: () => (/* binding */ createChain)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/observable */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/observable/index.mjs\");\n\n\n/** @internal */ function createChain(opts) {\n    return (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.observable)((observer)=>{\n        function execute(index = 0, op = opts.op) {\n            const next = opts.links[index];\n            if (!next) {\n                throw new Error('No more links to execute - did you forget to add an ending link?');\n            }\n            const subscription = next({\n                op,\n                next (nextOp) {\n                    const nextObserver = execute(index + 1, nextOp);\n                    return nextObserver;\n                }\n            });\n            return subscription;\n        }\n        const obs$ = execute();\n        return obs$.subscribe(observer);\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNC9ub2RlX21vZHVsZXMvQHRycGMvY2xpZW50L2Rpc3QvbGlua3MvaW50ZXJuYWxzL2NyZWF0ZUNoYWluLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDs7QUFFckQ7QUFDQSxXQUFXLG1FQUFVO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVIgMVxcRGVza3RvcFxcZGV2b3RzcC12MS41XFxwb3J0YWx3aXphcmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEB0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNFxcbm9kZV9tb2R1bGVzXFxAdHJwY1xcY2xpZW50XFxkaXN0XFxsaW5rc1xcaW50ZXJuYWxzXFxjcmVhdGVDaGFpbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgb2JzZXJ2YWJsZSB9IGZyb20gJ0B0cnBjL3NlcnZlci9vYnNlcnZhYmxlJztcblxuLyoqIEBpbnRlcm5hbCAqLyBmdW5jdGlvbiBjcmVhdGVDaGFpbihvcHRzKSB7XG4gICAgcmV0dXJuIG9ic2VydmFibGUoKG9ic2VydmVyKT0+e1xuICAgICAgICBmdW5jdGlvbiBleGVjdXRlKGluZGV4ID0gMCwgb3AgPSBvcHRzLm9wKSB7XG4gICAgICAgICAgICBjb25zdCBuZXh0ID0gb3B0cy5saW5rc1tpbmRleF07XG4gICAgICAgICAgICBpZiAoIW5leHQpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ05vIG1vcmUgbGlua3MgdG8gZXhlY3V0ZSAtIGRpZCB5b3UgZm9yZ2V0IHRvIGFkZCBhbiBlbmRpbmcgbGluaz8nKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHN1YnNjcmlwdGlvbiA9IG5leHQoe1xuICAgICAgICAgICAgICAgIG9wLFxuICAgICAgICAgICAgICAgIG5leHQgKG5leHRPcCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXh0T2JzZXJ2ZXIgPSBleGVjdXRlKGluZGV4ICsgMSwgbmV4dE9wKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5leHRPYnNlcnZlcjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybiBzdWJzY3JpcHRpb247XG4gICAgICAgIH1cbiAgICAgICAgY29uc3Qgb2JzJCA9IGV4ZWN1dGUoKTtcbiAgICAgICAgcmV0dXJuIG9icyQuc3Vic2NyaWJlKG9ic2VydmVyKTtcbiAgICB9KTtcbn1cblxuZXhwb3J0IHsgY3JlYXRlQ2hhaW4gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/createChain.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/httpUtils.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/httpUtils.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchHTTPResponse: () => (/* binding */ fetchHTTPResponse),\n/* harmony export */   getBody: () => (/* binding */ getBody),\n/* harmony export */   getInput: () => (/* binding */ getInput),\n/* harmony export */   getUrl: () => (/* binding */ getUrl),\n/* harmony export */   httpRequest: () => (/* binding */ httpRequest),\n/* harmony export */   jsonHttpRequester: () => (/* binding */ jsonHttpRequester),\n/* harmony export */   resolveHTTPLinkOptions: () => (/* binding */ resolveHTTPLinkOptions)\n/* harmony export */ });\n/* harmony import */ var _getFetch_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../getFetch.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/getFetch.mjs\");\n/* harmony import */ var _internals_transformer_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../internals/transformer.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/transformer.mjs\");\n\n\n\nfunction resolveHTTPLinkOptions(opts) {\n    return {\n        url: opts.url.toString(),\n        fetch: opts.fetch,\n        transformer: (0,_internals_transformer_mjs__WEBPACK_IMPORTED_MODULE_1__.getTransformer)(opts.transformer),\n        methodOverride: opts.methodOverride\n    };\n}\n// https://github.com/trpc/trpc/pull/669\nfunction arrayToDict(array) {\n    const dict = {};\n    for(let index = 0; index < array.length; index++){\n        const element = array[index];\n        dict[index] = element;\n    }\n    return dict;\n}\nconst METHOD = {\n    query: 'GET',\n    mutation: 'POST',\n    subscription: 'PATCH'\n};\nfunction getInput(opts) {\n    return 'input' in opts ? opts.transformer.input.serialize(opts.input) : arrayToDict(opts.inputs.map((_input)=>opts.transformer.input.serialize(_input)));\n}\nconst getUrl = (opts)=>{\n    const parts = opts.url.split('?');\n    const base = parts[0].replace(/\\/$/, ''); // Remove any trailing slashes\n    let url = base + '/' + opts.path;\n    const queryParts = [];\n    if (parts[1]) {\n        queryParts.push(parts[1]);\n    }\n    if ('inputs' in opts) {\n        queryParts.push('batch=1');\n    }\n    if (opts.type === 'query' || opts.type === 'subscription') {\n        const input = getInput(opts);\n        if (input !== undefined && opts.methodOverride !== 'POST') {\n            queryParts.push(`input=${encodeURIComponent(JSON.stringify(input))}`);\n        }\n    }\n    if (queryParts.length) {\n        url += '?' + queryParts.join('&');\n    }\n    return url;\n};\nconst getBody = (opts)=>{\n    if (opts.type === 'query' && opts.methodOverride !== 'POST') {\n        return undefined;\n    }\n    const input = getInput(opts);\n    return input !== undefined ? JSON.stringify(input) : undefined;\n};\nconst jsonHttpRequester = (opts)=>{\n    return httpRequest({\n        ...opts,\n        contentTypeHeader: 'application/json',\n        getUrl,\n        getBody\n    });\n};\n/**\n * Polyfill for DOMException with AbortError name\n */ class AbortError extends Error {\n    constructor(){\n        const name = 'AbortError';\n        super(name);\n        this.name = name;\n        this.message = name;\n    }\n}\n/**\n * Polyfill for `signal.throwIfAborted()`\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/throwIfAborted\n */ const throwIfAborted = (signal)=>{\n    if (!signal?.aborted) {\n        return;\n    }\n    // If available, use the native implementation\n    signal.throwIfAborted?.();\n    // If we have `DOMException`, use it\n    if (typeof DOMException !== 'undefined') {\n        throw new DOMException('AbortError', 'AbortError');\n    }\n    // Otherwise, use our own implementation\n    throw new AbortError();\n};\nasync function fetchHTTPResponse(opts) {\n    throwIfAborted(opts.signal);\n    const url = opts.getUrl(opts);\n    const body = opts.getBody(opts);\n    const { type } = opts;\n    const resolvedHeaders = await (async ()=>{\n        const heads = await opts.headers();\n        if (Symbol.iterator in heads) {\n            return Object.fromEntries(heads);\n        }\n        return heads;\n    })();\n    const headers = {\n        ...opts.contentTypeHeader ? {\n            'content-type': opts.contentTypeHeader\n        } : {},\n        ...opts.trpcAcceptHeader ? {\n            'trpc-accept': opts.trpcAcceptHeader\n        } : undefined,\n        ...resolvedHeaders\n    };\n    return (0,_getFetch_mjs__WEBPACK_IMPORTED_MODULE_0__.getFetch)(opts.fetch)(url, {\n        method: opts.methodOverride ?? METHOD[type],\n        signal: opts.signal,\n        body,\n        headers\n    });\n}\nasync function httpRequest(opts) {\n    const meta = {};\n    const res = await fetchHTTPResponse(opts);\n    meta.response = res;\n    const json = await res.json();\n    meta.responseJSON = json;\n    return {\n        json: json,\n        meta\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/httpUtils.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/urlWithConnectionParams.mjs":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/urlWithConnectionParams.mjs ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resultOf: () => (/* binding */ resultOf)\n/* harmony export */ });\n/**\n * Get the result of a value or function that returns a value\n * It also optionally accepts typesafe arguments for the function\n */ const resultOf = (value, ...args)=>{\n    return typeof value === 'function' ? value(...args) : value;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNC9ub2RlX21vZHVsZXMvQHRycGMvY2xpZW50L2Rpc3QvbGlua3MvaW50ZXJuYWxzL3VybFdpdGhDb25uZWN0aW9uUGFyYW1zLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVvQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSIDFcXERlc2t0b3BcXGRldm90c3AtdjEuNVxccG9ydGFsd2l6YXJkXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAdHJwYytjbGllbnRAMTEuMS4yX0B0cnBjK3NfZmM5NjQzY2NhMGM4OGZkNDcxY2I2NjQyMGNmMjJkMzRcXG5vZGVfbW9kdWxlc1xcQHRycGNcXGNsaWVudFxcZGlzdFxcbGlua3NcXGludGVybmFsc1xcdXJsV2l0aENvbm5lY3Rpb25QYXJhbXMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogR2V0IHRoZSByZXN1bHQgb2YgYSB2YWx1ZSBvciBmdW5jdGlvbiB0aGF0IHJldHVybnMgYSB2YWx1ZVxuICogSXQgYWxzbyBvcHRpb25hbGx5IGFjY2VwdHMgdHlwZXNhZmUgYXJndW1lbnRzIGZvciB0aGUgZnVuY3Rpb25cbiAqLyBjb25zdCByZXN1bHRPZiA9ICh2YWx1ZSwgLi4uYXJncyk9PntcbiAgICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSAnZnVuY3Rpb24nID8gdmFsdWUoLi4uYXJncykgOiB2YWx1ZTtcbn07XG5cbmV4cG9ydCB7IHJlc3VsdE9mIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/urlWithConnectionParams.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/loggerLink.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/loggerLink.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loggerLink: () => (/* binding */ loggerLink)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/observable */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/observable/index.mjs\");\n\n\n/// <reference lib=\"dom.iterable\" />\n// `dom.iterable` types are explicitly required for extracting `FormData` values,\n// as all implementations of `Symbol.iterable` are separated from the main `dom` types.\n// Using triple-slash directive makes sure that it will be available,\n// even if end-user `tsconfig.json` omits it in the `lib` array.\nfunction isFormData(value) {\n    if (typeof FormData === 'undefined') {\n        // FormData is not supported\n        return false;\n    }\n    return value instanceof FormData;\n}\nconst palettes = {\n    css: {\n        query: [\n            '72e3ff',\n            '3fb0d8'\n        ],\n        mutation: [\n            'c5a3fc',\n            '904dfc'\n        ],\n        subscription: [\n            'ff49e1',\n            'd83fbe'\n        ]\n    },\n    ansi: {\n        regular: {\n            // Cyan background, black and white text respectively\n            query: [\n                '\\x1b[30;46m',\n                '\\x1b[97;46m'\n            ],\n            // Magenta background, black and white text respectively\n            mutation: [\n                '\\x1b[30;45m',\n                '\\x1b[97;45m'\n            ],\n            // Green background, black and white text respectively\n            subscription: [\n                '\\x1b[30;42m',\n                '\\x1b[97;42m'\n            ]\n        },\n        bold: {\n            query: [\n                '\\x1b[1;30;46m',\n                '\\x1b[1;97;46m'\n            ],\n            mutation: [\n                '\\x1b[1;30;45m',\n                '\\x1b[1;97;45m'\n            ],\n            subscription: [\n                '\\x1b[1;30;42m',\n                '\\x1b[1;97;42m'\n            ]\n        }\n    }\n};\nfunction constructPartsAndArgs(opts) {\n    const { direction, type, withContext, path, id, input } = opts;\n    const parts = [];\n    const args = [];\n    if (opts.colorMode === 'none') {\n        parts.push(direction === 'up' ? '>>' : '<<', type, `#${id}`, path);\n    } else if (opts.colorMode === 'ansi') {\n        const [lightRegular, darkRegular] = palettes.ansi.regular[type];\n        const [lightBold, darkBold] = palettes.ansi.bold[type];\n        const reset = '\\x1b[0m';\n        parts.push(direction === 'up' ? lightRegular : darkRegular, direction === 'up' ? '>>' : '<<', type, direction === 'up' ? lightBold : darkBold, `#${id}`, path, reset);\n    } else {\n        // css color mode\n        const [light, dark] = palettes.css[type];\n        const css = `\n    background-color: #${direction === 'up' ? light : dark};\n    color: ${direction === 'up' ? 'black' : 'white'};\n    padding: 2px;\n  `;\n        parts.push('%c', direction === 'up' ? '>>' : '<<', type, `#${id}`, `%c${path}%c`, '%O');\n        args.push(css, `${css}; font-weight: bold;`, `${css}; font-weight: normal;`);\n    }\n    if (direction === 'up') {\n        args.push(withContext ? {\n            input,\n            context: opts.context\n        } : {\n            input\n        });\n    } else {\n        args.push({\n            input,\n            result: opts.result,\n            elapsedMs: opts.elapsedMs,\n            ...withContext && {\n                context: opts.context\n            }\n        });\n    }\n    return {\n        parts,\n        args\n    };\n}\n// maybe this should be moved to it's own package\nconst defaultLogger = ({ c = console, colorMode = 'css', withContext })=>(props)=>{\n        const rawInput = props.input;\n        const input = isFormData(rawInput) ? Object.fromEntries(rawInput) : rawInput;\n        const { parts, args } = constructPartsAndArgs({\n            ...props,\n            colorMode,\n            input,\n            withContext\n        });\n        const fn = props.direction === 'down' && props.result && (props.result instanceof Error || 'error' in props.result.result && props.result.result.error) ? 'error' : 'log';\n        c[fn].apply(null, [\n            parts.join(' ')\n        ].concat(args));\n    };\n/**\n * @see https://trpc.io/docs/v11/client/links/loggerLink\n */ function loggerLink(opts = {}) {\n    const { enabled = ()=>true } = opts;\n    const colorMode = opts.colorMode ?? (typeof window === 'undefined' ? 'ansi' : 'css');\n    const withContext = opts.withContext ?? colorMode === 'css';\n    const { logger = defaultLogger({\n        c: opts.console,\n        colorMode,\n        withContext\n    }) } = opts;\n    return ()=>{\n        return ({ op, next })=>{\n            return (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.observable)((observer)=>{\n                // ->\n                if (enabled({\n                    ...op,\n                    direction: 'up'\n                })) {\n                    logger({\n                        ...op,\n                        direction: 'up'\n                    });\n                }\n                const requestStartTime = Date.now();\n                function logResult(result) {\n                    const elapsedMs = Date.now() - requestStartTime;\n                    if (enabled({\n                        ...op,\n                        direction: 'down',\n                        result\n                    })) {\n                        logger({\n                            ...op,\n                            direction: 'down',\n                            elapsedMs,\n                            result\n                        });\n                    }\n                }\n                return next(op).pipe((0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.tap)({\n                    next (result) {\n                        logResult(result);\n                    },\n                    error (result) {\n                        logResult(result);\n                    }\n                })).subscribe(observer);\n            });\n        };\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/loggerLink.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/retryLink.mjs":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/retryLink.mjs ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   retryLink: () => (/* binding */ retryLink)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/observable */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/observable/index.mjs\");\n/* harmony import */ var _internals_inputWithTrackedEventId_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../internals/inputWithTrackedEventId.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/inputWithTrackedEventId.mjs\");\n\n\n\n/* istanbul ignore file -- @preserve */ // We're not actually exporting this link\n/**\n * @see https://trpc.io/docs/v11/client/links/retryLink\n */ function retryLink(opts) {\n    // initialized config\n    return ()=>{\n        // initialized in app\n        return (callOpts)=>{\n            // initialized for request\n            return (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.observable)((observer)=>{\n                let next$;\n                let callNextTimeout = undefined;\n                let lastEventId = undefined;\n                attempt(1);\n                function opWithLastEventId() {\n                    const op = callOpts.op;\n                    if (!lastEventId) {\n                        return op;\n                    }\n                    return {\n                        ...op,\n                        input: (0,_internals_inputWithTrackedEventId_mjs__WEBPACK_IMPORTED_MODULE_1__.inputWithTrackedEventId)(op.input, lastEventId)\n                    };\n                }\n                function attempt(attempts) {\n                    const op = opWithLastEventId();\n                    next$ = callOpts.next(op).subscribe({\n                        error (error) {\n                            const shouldRetry = opts.retry({\n                                op,\n                                attempts,\n                                error\n                            });\n                            if (!shouldRetry) {\n                                observer.error(error);\n                                return;\n                            }\n                            const delayMs = opts.retryDelayMs?.(attempts) ?? 0;\n                            if (delayMs <= 0) {\n                                attempt(attempts + 1);\n                                return;\n                            }\n                            callNextTimeout = setTimeout(()=>attempt(attempts + 1), delayMs);\n                        },\n                        next (envelope) {\n                            //\n                            if ((!envelope.result.type || envelope.result.type === 'data') && envelope.result.id) {\n                                //\n                                lastEventId = envelope.result.id;\n                            }\n                            observer.next(envelope);\n                        },\n                        complete () {\n                            observer.complete();\n                        }\n                    });\n                }\n                return ()=>{\n                    next$.unsubscribe();\n                    clearTimeout(callNextTimeout);\n                };\n            });\n        };\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/retryLink.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/splitLink.mjs":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/splitLink.mjs ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   splitLink: () => (/* binding */ splitLink)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/observable */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/observable/index.mjs\");\n/* harmony import */ var _internals_createChain_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./internals/createChain.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/createChain.mjs\");\n\n\n\nfunction asArray(value) {\n    return Array.isArray(value) ? value : [\n        value\n    ];\n}\nfunction splitLink(opts) {\n    return (runtime)=>{\n        const yes = asArray(opts.true).map((link)=>link(runtime));\n        const no = asArray(opts.false).map((link)=>link(runtime));\n        return (props)=>{\n            return (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.observable)((observer)=>{\n                const links = opts.condition(props.op) ? yes : no;\n                return (0,_internals_createChain_mjs__WEBPACK_IMPORTED_MODULE_1__.createChain)({\n                    op: props.op,\n                    links\n                }).subscribe(observer);\n            });\n        };\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNC9ub2RlX21vZHVsZXMvQHRycGMvY2xpZW50L2Rpc3QvbGlua3Mvc3BsaXRMaW5rLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUQ7QUFDSzs7QUFFMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsbUVBQVU7QUFDN0I7QUFDQSx1QkFBdUIsdUVBQVc7QUFDbEM7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBOztBQUVxQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSIDFcXERlc2t0b3BcXGRldm90c3AtdjEuNVxccG9ydGFsd2l6YXJkXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAdHJwYytjbGllbnRAMTEuMS4yX0B0cnBjK3NfZmM5NjQzY2NhMGM4OGZkNDcxY2I2NjQyMGNmMjJkMzRcXG5vZGVfbW9kdWxlc1xcQHRycGNcXGNsaWVudFxcZGlzdFxcbGlua3NcXHNwbGl0TGluay5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgb2JzZXJ2YWJsZSB9IGZyb20gJ0B0cnBjL3NlcnZlci9vYnNlcnZhYmxlJztcbmltcG9ydCB7IGNyZWF0ZUNoYWluIH0gZnJvbSAnLi9pbnRlcm5hbHMvY3JlYXRlQ2hhaW4ubWpzJztcblxuZnVuY3Rpb24gYXNBcnJheSh2YWx1ZSkge1xuICAgIHJldHVybiBBcnJheS5pc0FycmF5KHZhbHVlKSA/IHZhbHVlIDogW1xuICAgICAgICB2YWx1ZVxuICAgIF07XG59XG5mdW5jdGlvbiBzcGxpdExpbmsob3B0cykge1xuICAgIHJldHVybiAocnVudGltZSk9PntcbiAgICAgICAgY29uc3QgeWVzID0gYXNBcnJheShvcHRzLnRydWUpLm1hcCgobGluayk9PmxpbmsocnVudGltZSkpO1xuICAgICAgICBjb25zdCBubyA9IGFzQXJyYXkob3B0cy5mYWxzZSkubWFwKChsaW5rKT0+bGluayhydW50aW1lKSk7XG4gICAgICAgIHJldHVybiAocHJvcHMpPT57XG4gICAgICAgICAgICByZXR1cm4gb2JzZXJ2YWJsZSgob2JzZXJ2ZXIpPT57XG4gICAgICAgICAgICAgICAgY29uc3QgbGlua3MgPSBvcHRzLmNvbmRpdGlvbihwcm9wcy5vcCkgPyB5ZXMgOiBubztcbiAgICAgICAgICAgICAgICByZXR1cm4gY3JlYXRlQ2hhaW4oe1xuICAgICAgICAgICAgICAgICAgICBvcDogcHJvcHMub3AsXG4gICAgICAgICAgICAgICAgICAgIGxpbmtzXG4gICAgICAgICAgICAgICAgfSkuc3Vic2NyaWJlKG9ic2VydmVyKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgIH07XG59XG5cbmV4cG9ydCB7IHNwbGl0TGluayB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/splitLink.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/createWsClient.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/createWsClient.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createWSClient: () => (/* binding */ createWSClient)\n/* harmony export */ });\n/* harmony import */ var _wsClient_wsClient_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./wsClient/wsClient.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/wsClient.mjs\");\n\n\nfunction createWSClient(opts) {\n    return new _wsClient_wsClient_mjs__WEBPACK_IMPORTED_MODULE_0__.WsClient(opts);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNC9ub2RlX21vZHVsZXMvQHRycGMvY2xpZW50L2Rpc3QvbGlua3Mvd3NMaW5rL2NyZWF0ZVdzQ2xpZW50Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRDs7QUFFbkQ7QUFDQSxlQUFlLDREQUFRO0FBQ3ZCOztBQUUwQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSIDFcXERlc2t0b3BcXGRldm90c3AtdjEuNVxccG9ydGFsd2l6YXJkXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAdHJwYytjbGllbnRAMTEuMS4yX0B0cnBjK3NfZmM5NjQzY2NhMGM4OGZkNDcxY2I2NjQyMGNmMjJkMzRcXG5vZGVfbW9kdWxlc1xcQHRycGNcXGNsaWVudFxcZGlzdFxcbGlua3NcXHdzTGlua1xcY3JlYXRlV3NDbGllbnQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFdzQ2xpZW50IH0gZnJvbSAnLi93c0NsaWVudC93c0NsaWVudC5tanMnO1xuXG5mdW5jdGlvbiBjcmVhdGVXU0NsaWVudChvcHRzKSB7XG4gICAgcmV0dXJuIG5ldyBXc0NsaWVudChvcHRzKTtcbn1cblxuZXhwb3J0IHsgY3JlYXRlV1NDbGllbnQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/createWsClient.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/options.mjs":
/*!**************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/options.mjs ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exponentialBackoff: () => (/* binding */ exponentialBackoff),\n/* harmony export */   keepAliveDefaults: () => (/* binding */ keepAliveDefaults),\n/* harmony export */   lazyDefaults: () => (/* binding */ lazyDefaults)\n/* harmony export */ });\nconst lazyDefaults = {\n    enabled: false,\n    closeMs: 0\n};\nconst keepAliveDefaults = {\n    enabled: false,\n    pongTimeoutMs: 1000,\n    intervalMs: 5000\n};\n/**\n * Calculates a delay for exponential backoff based on the retry attempt index.\n * The delay starts at 0 for the first attempt and doubles for each subsequent attempt,\n * capped at 30 seconds.\n */ const exponentialBackoff = (attemptIndex)=>{\n    return attemptIndex === 0 ? 0 : Math.min(1000 * 2 ** attemptIndex, 30000);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0cnBjK2NsaWVudEAxMS4xLjJfQHRycGMrc19mYzk2NDNjY2EwYzg4ZmQ0NzFjYjY2NDIwY2YyMmQzNC9ub2RlX21vZHVsZXMvQHRycGMvY2xpZW50L2Rpc3QvbGlua3Mvd3NMaW5rL3dzQ2xpZW50L29wdGlvbnMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUrRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSIDFcXERlc2t0b3BcXGRldm90c3AtdjEuNVxccG9ydGFsd2l6YXJkXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAdHJwYytjbGllbnRAMTEuMS4yX0B0cnBjK3NfZmM5NjQzY2NhMGM4OGZkNDcxY2I2NjQyMGNmMjJkMzRcXG5vZGVfbW9kdWxlc1xcQHRycGNcXGNsaWVudFxcZGlzdFxcbGlua3NcXHdzTGlua1xcd3NDbGllbnRcXG9wdGlvbnMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGxhenlEZWZhdWx0cyA9IHtcbiAgICBlbmFibGVkOiBmYWxzZSxcbiAgICBjbG9zZU1zOiAwXG59O1xuY29uc3Qga2VlcEFsaXZlRGVmYXVsdHMgPSB7XG4gICAgZW5hYmxlZDogZmFsc2UsXG4gICAgcG9uZ1RpbWVvdXRNczogMTAwMCxcbiAgICBpbnRlcnZhbE1zOiA1MDAwXG59O1xuLyoqXG4gKiBDYWxjdWxhdGVzIGEgZGVsYXkgZm9yIGV4cG9uZW50aWFsIGJhY2tvZmYgYmFzZWQgb24gdGhlIHJldHJ5IGF0dGVtcHQgaW5kZXguXG4gKiBUaGUgZGVsYXkgc3RhcnRzIGF0IDAgZm9yIHRoZSBmaXJzdCBhdHRlbXB0IGFuZCBkb3VibGVzIGZvciBlYWNoIHN1YnNlcXVlbnQgYXR0ZW1wdCxcbiAqIGNhcHBlZCBhdCAzMCBzZWNvbmRzLlxuICovIGNvbnN0IGV4cG9uZW50aWFsQmFja29mZiA9IChhdHRlbXB0SW5kZXgpPT57XG4gICAgcmV0dXJuIGF0dGVtcHRJbmRleCA9PT0gMCA/IDAgOiBNYXRoLm1pbigxMDAwICogMiAqKiBhdHRlbXB0SW5kZXgsIDMwMDAwKTtcbn07XG5cbmV4cG9ydCB7IGV4cG9uZW50aWFsQmFja29mZiwga2VlcEFsaXZlRGVmYXVsdHMsIGxhenlEZWZhdWx0cyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/options.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/requestManager.mjs":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/requestManager.mjs ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RequestManager: () => (/* binding */ RequestManager)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/utils.mjs\");\n\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n/**\n * Manages WebSocket requests, tracking their lifecycle and providing utility methods\n * for handling outgoing and pending requests.\n *\n * - **Outgoing requests**: Requests that are queued and waiting to be sent.\n * - **Pending requests**: Requests that have been sent and are in flight awaiting a response.\n *   For subscriptions, multiple responses may be received until the subscription is closed.\n */ class RequestManager {\n    /**\n   * Registers a new request by adding it to the outgoing queue and setting up\n   * callbacks for lifecycle events such as completion or error.\n   *\n   * @param message - The outgoing message to be sent.\n   * @param callbacks - Callback functions to observe the request's state.\n   * @returns A cleanup function to manually remove the request.\n   */ register(message, callbacks) {\n        const { promise: end, resolve } = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.withResolvers)();\n        this.outgoingRequests.push({\n            id: String(message.id),\n            message,\n            end,\n            callbacks: {\n                next: callbacks.next,\n                complete: ()=>{\n                    callbacks.complete();\n                    resolve();\n                },\n                error: (e)=>{\n                    callbacks.error(e);\n                    resolve();\n                }\n            }\n        });\n        return ()=>{\n            this.delete(message.id);\n            callbacks.complete();\n            resolve();\n        };\n    }\n    /**\n   * Deletes a request from both the outgoing and pending collections, if it exists.\n   */ delete(messageId) {\n        if (messageId === null) return;\n        this.outgoingRequests = this.outgoingRequests.filter(({ id })=>id !== String(messageId));\n        delete this.pendingRequests[String(messageId)];\n    }\n    /**\n   * Moves all outgoing requests to the pending state and clears the outgoing queue.\n   *\n   * The caller is expected to handle the actual sending of the requests\n   * (e.g., sending them over the network) after this method is called.\n   *\n   * @returns The list of requests that were transitioned to the pending state.\n   */ flush() {\n        const requests = this.outgoingRequests;\n        this.outgoingRequests = [];\n        for (const request of requests){\n            this.pendingRequests[request.id] = request;\n        }\n        return requests;\n    }\n    /**\n   * Retrieves all currently pending requests, which are in flight awaiting responses\n   * or handling ongoing subscriptions.\n   */ getPendingRequests() {\n        return Object.values(this.pendingRequests);\n    }\n    /**\n   * Retrieves a specific pending request by its message ID.\n   */ getPendingRequest(messageId) {\n        if (messageId === null) return null;\n        return this.pendingRequests[String(messageId)];\n    }\n    /**\n   * Retrieves all outgoing requests, which are waiting to be sent.\n   */ getOutgoingRequests() {\n        return this.outgoingRequests;\n    }\n    /**\n   * Retrieves all requests, both outgoing and pending, with their respective states.\n   *\n   * @returns An array of all requests with their state (\"outgoing\" or \"pending\").\n   */ getRequests() {\n        return [\n            ...this.getOutgoingRequests().map((request)=>({\n                    state: 'outgoing',\n                    message: request.message,\n                    end: request.end,\n                    callbacks: request.callbacks\n                })),\n            ...this.getPendingRequests().map((request)=>({\n                    state: 'pending',\n                    message: request.message,\n                    end: request.end,\n                    callbacks: request.callbacks\n                }))\n        ];\n    }\n    /**\n   * Checks if there are any pending requests, including ongoing subscriptions.\n   */ hasPendingRequests() {\n        return this.getPendingRequests().length > 0;\n    }\n    /**\n   * Checks if there are any outgoing requests waiting to be sent.\n   */ hasOutgoingRequests() {\n        return this.outgoingRequests.length > 0;\n    }\n    constructor(){\n        /**\n   * Stores requests that are outgoing, meaning they are registered but not yet sent over the WebSocket.\n   */ _define_property(this, \"outgoingRequests\", new Array());\n        /**\n   * Stores requests that are pending (in flight), meaning they have been sent over the WebSocket\n   * and are awaiting responses. For subscriptions, this includes requests\n   * that may receive multiple responses.\n   */ _define_property(this, \"pendingRequests\", {});\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/requestManager.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/utils.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/utils.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResettableTimeout: () => (/* binding */ ResettableTimeout),\n/* harmony export */   TRPCWebSocketClosedError: () => (/* binding */ TRPCWebSocketClosedError),\n/* harmony export */   buildConnectionMessage: () => (/* binding */ buildConnectionMessage),\n/* harmony export */   prepareUrl: () => (/* binding */ prepareUrl),\n/* harmony export */   withResolvers: () => (/* binding */ withResolvers)\n/* harmony export */ });\n/* harmony import */ var _internals_urlWithConnectionParams_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../internals/urlWithConnectionParams.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/internals/urlWithConnectionParams.mjs\");\n\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nclass TRPCWebSocketClosedError extends Error {\n    constructor(opts){\n        super(opts.message, {\n            cause: opts.cause\n        });\n        this.name = 'TRPCWebSocketClosedError';\n        Object.setPrototypeOf(this, TRPCWebSocketClosedError.prototype);\n    }\n}\n/**\n * Utility class for managing a timeout that can be started, stopped, and reset.\n * Useful for scenarios where the timeout duration is reset dynamically based on events.\n */ class ResettableTimeout {\n    /**\n   * Resets the current timeout, restarting it with the same duration.\n   * Does nothing if no timeout is active.\n   */ reset() {\n        if (!this.timeout) return;\n        clearTimeout(this.timeout);\n        this.timeout = setTimeout(this.onTimeout, this.timeoutMs);\n    }\n    start() {\n        clearTimeout(this.timeout);\n        this.timeout = setTimeout(this.onTimeout, this.timeoutMs);\n    }\n    stop() {\n        clearTimeout(this.timeout);\n        this.timeout = undefined;\n    }\n    constructor(onTimeout, timeoutMs){\n        _define_property(this, \"onTimeout\", void 0);\n        _define_property(this, \"timeoutMs\", void 0);\n        _define_property(this, \"timeout\", void 0);\n        this.onTimeout = onTimeout;\n        this.timeoutMs = timeoutMs;\n    }\n}\n// Ponyfill for Promise.withResolvers https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/withResolvers\nfunction withResolvers() {\n    let resolve;\n    let reject;\n    const promise = new Promise((res, rej)=>{\n        resolve = res;\n        reject = rej;\n    });\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return {\n        promise,\n        resolve: resolve,\n        reject: reject\n    };\n}\n/**\n * Resolves a WebSocket URL and optionally appends connection parameters.\n *\n * If connectionParams are provided, appends 'connectionParams=1' query parameter.\n */ async function prepareUrl(urlOptions) {\n    const url = await (0,_internals_urlWithConnectionParams_mjs__WEBPACK_IMPORTED_MODULE_0__.resultOf)(urlOptions.url);\n    if (!urlOptions.connectionParams) return url;\n    // append `?connectionParams=1` when connection params are used\n    const prefix = url.includes('?') ? '&' : '?';\n    const connectionParams = `${prefix}connectionParams=1`;\n    return url + connectionParams;\n}\nasync function buildConnectionMessage(connectionParams) {\n    const message = {\n        method: 'connectionParams',\n        data: await (0,_internals_urlWithConnectionParams_mjs__WEBPACK_IMPORTED_MODULE_0__.resultOf)(connectionParams)\n    };\n    return JSON.stringify(message);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/utils.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/wsClient.mjs":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/wsClient.mjs ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WsClient: () => (/* binding */ WsClient)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/observable */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/observable/index.mjs\");\n/* harmony import */ var _trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @trpc/server/unstable-core-do-not-import */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/unstable-core-do-not-import.mjs\");\n/* harmony import */ var _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../TRPCClientError.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/TRPCClientError.mjs\");\n/* harmony import */ var _options_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./options.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/options.mjs\");\n/* harmony import */ var _requestManager_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./requestManager.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/requestManager.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/utils.mjs\");\n/* harmony import */ var _wsConnection_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./wsConnection.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/wsConnection.mjs\");\n\n\n\n\n\n\n\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n/**\n * A WebSocket client for managing TRPC operations, supporting lazy initialization,\n * reconnection, keep-alive, and request management.\n */ class WsClient {\n    /**\n   * Opens the WebSocket connection. Handles reconnection attempts and updates\n   * the connection state accordingly.\n   */ async open() {\n        this.allowReconnect = true;\n        if (this.connectionState.get().state !== 'connecting') {\n            this.connectionState.next({\n                type: 'state',\n                state: 'connecting',\n                error: null\n            });\n        }\n        try {\n            await this.activeConnection.open();\n        } catch (error) {\n            this.reconnect(new _utils_mjs__WEBPACK_IMPORTED_MODULE_5__.TRPCWebSocketClosedError({\n                message: 'Initialization error',\n                cause: error\n            }));\n            return this.reconnecting;\n        }\n    }\n    /**\n   * Closes the WebSocket connection and stops managing requests.\n   * Ensures all outgoing and pending requests are properly finalized.\n   */ async close() {\n        this.allowReconnect = false;\n        this.inactivityTimeout.stop();\n        const requestsToAwait = [];\n        for (const request of this.requestManager.getRequests()){\n            if (request.message.method === 'subscription') {\n                request.callbacks.complete();\n            } else if (request.state === 'outgoing') {\n                request.callbacks.error(_TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_2__.TRPCClientError.from(new _utils_mjs__WEBPACK_IMPORTED_MODULE_5__.TRPCWebSocketClosedError({\n                    message: 'Closed before connection was established'\n                })));\n            } else {\n                requestsToAwait.push(request.end);\n            }\n        }\n        await Promise.all(requestsToAwait).catch(()=>null);\n        await this.activeConnection.close().catch(()=>null);\n        this.connectionState.next({\n            type: 'state',\n            state: 'idle',\n            error: null\n        });\n    }\n    /**\n   * Method to request the server.\n   * Handles data transformation, batching of requests, and subscription lifecycle.\n   *\n   * @param op - The operation details including id, type, path, input and signal\n   * @param transformer - Data transformer for serializing requests and deserializing responses\n   * @param lastEventId - Optional ID of the last received event for subscriptions\n   *\n   * @returns An observable that emits operation results and handles cleanup\n   */ request({ op: { id, type, path, input, signal }, transformer, lastEventId }) {\n        return (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.observable)((observer)=>{\n            const abort = this.batchSend({\n                id,\n                method: type,\n                params: {\n                    input: transformer.input.serialize(input),\n                    path,\n                    lastEventId\n                }\n            }, {\n                ...observer,\n                next (event) {\n                    const transformed = (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_1__.transformResult)(event, transformer.output);\n                    if (!transformed.ok) {\n                        observer.error(_TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_2__.TRPCClientError.from(transformed.error));\n                        return;\n                    }\n                    observer.next({\n                        result: transformed.result\n                    });\n                }\n            });\n            return ()=>{\n                abort();\n                if (type === 'subscription' && this.activeConnection.isOpen()) {\n                    this.send({\n                        id,\n                        method: 'subscription.stop'\n                    });\n                }\n                signal?.removeEventListener('abort', abort);\n            };\n        });\n    }\n    get connection() {\n        return (0,_wsConnection_mjs__WEBPACK_IMPORTED_MODULE_6__.backwardCompatibility)(this.activeConnection);\n    }\n    reconnect(closedError) {\n        this.connectionState.next({\n            type: 'state',\n            state: 'connecting',\n            error: _TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_2__.TRPCClientError.from(closedError)\n        });\n        if (this.reconnecting) return;\n        const tryReconnect = async (attemptIndex)=>{\n            try {\n                await (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_1__.sleep)(this.reconnectRetryDelay(attemptIndex));\n                if (this.allowReconnect) {\n                    await this.activeConnection.close();\n                    await this.activeConnection.open();\n                    if (this.requestManager.hasPendingRequests()) {\n                        this.send(this.requestManager.getPendingRequests().map(({ message })=>message));\n                    }\n                }\n                this.reconnecting = null;\n            } catch  {\n                await tryReconnect(attemptIndex + 1);\n            }\n        };\n        this.reconnecting = tryReconnect(0);\n    }\n    setupWebSocketListeners(ws) {\n        const handleCloseOrError = (cause)=>{\n            const reqs = this.requestManager.getPendingRequests();\n            for (const { message, callbacks } of reqs){\n                if (message.method === 'subscription') continue;\n                callbacks.error(_TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_2__.TRPCClientError.from(cause ?? new _utils_mjs__WEBPACK_IMPORTED_MODULE_5__.TRPCWebSocketClosedError({\n                    message: 'WebSocket closed',\n                    cause\n                })));\n                this.requestManager.delete(message.id);\n            }\n        };\n        ws.addEventListener('open', ()=>{\n            (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_1__.run)(async ()=>{\n                if (this.lazyMode) {\n                    this.inactivityTimeout.start();\n                }\n                this.callbacks.onOpen?.();\n                this.connectionState.next({\n                    type: 'state',\n                    state: 'pending',\n                    error: null\n                });\n            }).catch((error)=>{\n                ws.close(3000);\n                handleCloseOrError(error);\n            });\n        });\n        ws.addEventListener('message', ({ data })=>{\n            this.inactivityTimeout.reset();\n            if (typeof data !== 'string' || [\n                'PING',\n                'PONG'\n            ].includes(data)) return;\n            const incomingMessage = JSON.parse(data);\n            if ('method' in incomingMessage) {\n                this.handleIncomingRequest(incomingMessage);\n                return;\n            }\n            this.handleResponseMessage(incomingMessage);\n        });\n        ws.addEventListener('close', (event)=>{\n            handleCloseOrError(event);\n            this.callbacks.onClose?.(event);\n            if (!this.lazyMode) {\n                this.reconnect(new _utils_mjs__WEBPACK_IMPORTED_MODULE_5__.TRPCWebSocketClosedError({\n                    message: 'WebSocket closed',\n                    cause: event\n                }));\n            }\n        });\n        ws.addEventListener('error', (event)=>{\n            handleCloseOrError(event);\n            this.callbacks.onError?.(event);\n            this.reconnect(new _utils_mjs__WEBPACK_IMPORTED_MODULE_5__.TRPCWebSocketClosedError({\n                message: 'WebSocket closed',\n                cause: event\n            }));\n        });\n    }\n    handleResponseMessage(message) {\n        const request = this.requestManager.getPendingRequest(message.id);\n        if (!request) return;\n        request.callbacks.next(message);\n        let completed = true;\n        if ('result' in message && request.message.method === 'subscription') {\n            if (message.result.type === 'data') {\n                request.message.params.lastEventId = message.result.id;\n            }\n            if (message.result.type !== 'stopped') {\n                completed = false;\n            }\n        }\n        if (completed) {\n            request.callbacks.complete();\n            this.requestManager.delete(message.id);\n        }\n    }\n    handleIncomingRequest(message) {\n        if (message.method === 'reconnect') {\n            this.reconnect(new _utils_mjs__WEBPACK_IMPORTED_MODULE_5__.TRPCWebSocketClosedError({\n                message: 'Server requested reconnect'\n            }));\n        }\n    }\n    /**\n   * Sends a message or batch of messages directly to the server.\n   */ send(messageOrMessages) {\n        if (!this.activeConnection.isOpen()) {\n            throw new Error('Active connection is not open');\n        }\n        const messages = messageOrMessages instanceof Array ? messageOrMessages : [\n            messageOrMessages\n        ];\n        this.activeConnection.ws.send(JSON.stringify(messages.length === 1 ? messages[0] : messages));\n    }\n    /**\n   * Groups requests for batch sending.\n   *\n   * @returns A function to abort the batched request.\n   */ batchSend(message, callbacks) {\n        this.inactivityTimeout.reset();\n        (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_1__.run)(async ()=>{\n            if (!this.activeConnection.isOpen()) {\n                await this.open();\n            }\n            await (0,_trpc_server_unstable_core_do_not_import__WEBPACK_IMPORTED_MODULE_1__.sleep)(0);\n            if (!this.requestManager.hasOutgoingRequests()) return;\n            this.send(this.requestManager.flush().map(({ message })=>message));\n        }).catch((err)=>{\n            this.requestManager.delete(message.id);\n            callbacks.error(_TRPCClientError_mjs__WEBPACK_IMPORTED_MODULE_2__.TRPCClientError.from(err));\n        });\n        return this.requestManager.register(message, callbacks);\n    }\n    constructor(opts){\n        /**\n   * Observable tracking the current connection state, including errors.\n   */ _define_property(this, \"connectionState\", void 0);\n        _define_property(this, \"allowReconnect\", false);\n        _define_property(this, \"requestManager\", new _requestManager_mjs__WEBPACK_IMPORTED_MODULE_4__.RequestManager());\n        _define_property(this, \"activeConnection\", void 0);\n        _define_property(this, \"reconnectRetryDelay\", void 0);\n        _define_property(this, \"inactivityTimeout\", void 0);\n        _define_property(this, \"callbacks\", void 0);\n        _define_property(this, \"lazyMode\", void 0);\n        /**\n   * Manages the reconnection process for the WebSocket using retry logic.\n   * Ensures that only one reconnection attempt is active at a time by tracking the current\n   * reconnection state in the `reconnecting` promise.\n   */ _define_property(this, \"reconnecting\", null);\n        // Initialize callbacks, connection parameters, and options.\n        this.callbacks = {\n            onOpen: opts.onOpen,\n            onClose: opts.onClose,\n            onError: opts.onError\n        };\n        const lazyOptions = {\n            ..._options_mjs__WEBPACK_IMPORTED_MODULE_3__.lazyDefaults,\n            ...opts.lazy\n        };\n        // Set up inactivity timeout for lazy connections.\n        this.inactivityTimeout = new _utils_mjs__WEBPACK_IMPORTED_MODULE_5__.ResettableTimeout(()=>{\n            if (this.requestManager.hasOutgoingRequests() || this.requestManager.hasPendingRequests()) {\n                this.inactivityTimeout.reset();\n                return;\n            }\n            this.close().catch(()=>null);\n        }, lazyOptions.closeMs);\n        // Initialize the WebSocket connection.\n        this.activeConnection = new _wsConnection_mjs__WEBPACK_IMPORTED_MODULE_6__.WsConnection({\n            WebSocketPonyfill: opts.WebSocket,\n            urlOptions: opts,\n            keepAlive: {\n                ..._options_mjs__WEBPACK_IMPORTED_MODULE_3__.keepAliveDefaults,\n                ...opts.keepAlive\n            }\n        });\n        this.activeConnection.wsObservable.subscribe({\n            next: (ws)=>{\n                if (!ws) return;\n                this.setupWebSocketListeners(ws);\n            }\n        });\n        this.reconnectRetryDelay = opts.retryDelayMs ?? _options_mjs__WEBPACK_IMPORTED_MODULE_3__.exponentialBackoff;\n        this.lazyMode = lazyOptions.enabled;\n        this.connectionState = (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.behaviorSubject)({\n            type: 'state',\n            state: lazyOptions.enabled ? 'idle' : 'connecting',\n            error: null\n        });\n        // Automatically open the connection if lazy mode is disabled.\n        if (!this.lazyMode) {\n            this.open().catch(()=>null);\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/wsClient.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/wsConnection.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/wsConnection.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WsConnection: () => (/* binding */ WsConnection),\n/* harmony export */   backwardCompatibility: () => (/* binding */ backwardCompatibility)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/observable */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/observable/index.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/utils.mjs\");\n\n\n\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n/**\n * Opens a WebSocket connection asynchronously and returns a promise\n * that resolves when the connection is successfully established.\n * The promise rejects if an error occurs during the connection attempt.\n */ function asyncWsOpen(ws) {\n    const { promise, resolve, reject } = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.withResolvers)();\n    ws.addEventListener('open', ()=>{\n        ws.removeEventListener('error', reject);\n        resolve();\n    });\n    ws.addEventListener('error', reject);\n    return promise;\n}\n/**\n * Sets up a periodic ping-pong mechanism to keep the WebSocket connection alive.\n *\n * - Sends \"PING\" messages at regular intervals defined by `intervalMs`.\n * - If a \"PONG\" response is not received within the `pongTimeoutMs`, the WebSocket is closed.\n * - The ping timer resets upon receiving any message to maintain activity.\n * - Automatically starts the ping process when the WebSocket connection is opened.\n * - Cleans up timers when the WebSocket is closed.\n *\n * @param ws - The WebSocket instance to manage.\n * @param options - Configuration options for ping-pong intervals and timeouts.\n */ function setupPingInterval(ws, { intervalMs, pongTimeoutMs }) {\n    let pingTimeout;\n    let pongTimeout;\n    function start() {\n        pingTimeout = setTimeout(()=>{\n            ws.send('PING');\n            pongTimeout = setTimeout(()=>{\n                ws.close();\n            }, pongTimeoutMs);\n        }, intervalMs);\n    }\n    function reset() {\n        clearTimeout(pingTimeout);\n        start();\n    }\n    function pong() {\n        clearTimeout(pongTimeout);\n        reset();\n    }\n    ws.addEventListener('open', start);\n    ws.addEventListener('message', ({ data })=>{\n        clearTimeout(pingTimeout);\n        start();\n        if (data === 'PONG') {\n            pong();\n        }\n    });\n    ws.addEventListener('close', ()=>{\n        clearTimeout(pingTimeout);\n        clearTimeout(pongTimeout);\n    });\n}\n/**\n * Manages a WebSocket connection with support for reconnection, keep-alive mechanisms,\n * and observable state tracking.\n */ class WsConnection {\n    get ws() {\n        return this.wsObservable.get();\n    }\n    set ws(ws) {\n        this.wsObservable.next(ws);\n    }\n    /**\n   * Checks if the WebSocket connection is open and ready to communicate.\n   */ isOpen() {\n        return !!this.ws && this.ws.readyState === this.WebSocketPonyfill.OPEN && !this.openPromise;\n    }\n    /**\n   * Checks if the WebSocket connection is closed or in the process of closing.\n   */ isClosed() {\n        return !!this.ws && (this.ws.readyState === this.WebSocketPonyfill.CLOSING || this.ws.readyState === this.WebSocketPonyfill.CLOSED);\n    }\n    async open() {\n        if (this.openPromise) return this.openPromise;\n        this.id = ++WsConnection.connectCount;\n        const wsPromise = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.prepareUrl)(this.urlOptions).then((url)=>new this.WebSocketPonyfill(url));\n        this.openPromise = wsPromise.then(async (ws)=>{\n            this.ws = ws;\n            // Setup ping listener\n            ws.addEventListener('message', function({ data }) {\n                if (data === 'PING') {\n                    this.send('PONG');\n                }\n            });\n            if (this.keepAliveOpts.enabled) {\n                setupPingInterval(ws, this.keepAliveOpts);\n            }\n            ws.addEventListener('close', ()=>{\n                if (this.ws === ws) {\n                    this.ws = null;\n                }\n            });\n            await asyncWsOpen(ws);\n            if (this.urlOptions.connectionParams) {\n                ws.send(await (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.buildConnectionMessage)(this.urlOptions.connectionParams));\n            }\n        });\n        try {\n            await this.openPromise;\n        } finally{\n            this.openPromise = null;\n        }\n    }\n    /**\n   * Closes the WebSocket connection gracefully.\n   * Waits for any ongoing open operation to complete before closing.\n   */ async close() {\n        try {\n            await this.openPromise;\n        } finally{\n            this.ws?.close();\n        }\n    }\n    constructor(opts){\n        _define_property(this, \"id\", ++WsConnection.connectCount);\n        _define_property(this, \"WebSocketPonyfill\", void 0);\n        _define_property(this, \"urlOptions\", void 0);\n        _define_property(this, \"keepAliveOpts\", void 0);\n        _define_property(this, \"wsObservable\", (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.behaviorSubject)(null));\n        /**\n   * Manages the WebSocket opening process, ensuring that only one open operation\n   * occurs at a time. Tracks the ongoing operation with `openPromise` to avoid\n   * redundant calls and ensure proper synchronization.\n   *\n   * Sets up the keep-alive mechanism and necessary event listeners for the connection.\n   *\n   * @returns A promise that resolves once the WebSocket connection is successfully opened.\n   */ _define_property(this, \"openPromise\", null);\n        this.WebSocketPonyfill = opts.WebSocketPonyfill ?? WebSocket;\n        if (!this.WebSocketPonyfill) {\n            throw new Error(\"No WebSocket implementation found - you probably don't want to use this on the server, but if you do you need to pass a `WebSocket`-ponyfill\");\n        }\n        this.urlOptions = opts.urlOptions;\n        this.keepAliveOpts = opts.keepAlive;\n    }\n}\n_define_property(WsConnection, \"connectCount\", 0);\n/**\n * Provides a backward-compatible representation of the connection state.\n */ function backwardCompatibility(connection) {\n    if (connection.isOpen()) {\n        return {\n            id: connection.id,\n            state: 'open',\n            ws: connection.ws\n        };\n    }\n    if (connection.isClosed()) {\n        return {\n            id: connection.id,\n            state: 'closed',\n            ws: connection.ws\n        };\n    }\n    if (!connection.ws) {\n        return null;\n    }\n    return {\n        id: connection.id,\n        state: 'connecting',\n        ws: connection.ws\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsClient/wsConnection.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsLink.mjs":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsLink.mjs ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createWSClient: () => (/* reexport safe */ _createWsClient_mjs__WEBPACK_IMPORTED_MODULE_2__.createWSClient),\n/* harmony export */   wsLink: () => (/* binding */ wsLink)\n/* harmony export */ });\n/* harmony import */ var _trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @trpc/server/observable */ \"(ssr)/../../node_modules/.pnpm/@trpc+server@11.1.2_typescript@5.8.3/node_modules/@trpc/server/dist/observable/index.mjs\");\n/* harmony import */ var _internals_transformer_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../internals/transformer.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/internals/transformer.mjs\");\n/* harmony import */ var _createWsClient_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createWsClient.mjs */ \"(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/createWsClient.mjs\");\n\n\n\n\nfunction wsLink(opts) {\n    const { client } = opts;\n    const transformer = (0,_internals_transformer_mjs__WEBPACK_IMPORTED_MODULE_1__.getTransformer)(opts.transformer);\n    return ()=>{\n        return ({ op })=>{\n            return (0,_trpc_server_observable__WEBPACK_IMPORTED_MODULE_0__.observable)((observer)=>{\n                const connStateSubscription = op.type === 'subscription' ? client.connectionState.subscribe({\n                    next (result) {\n                        observer.next({\n                            result,\n                            context: op.context\n                        });\n                    }\n                }) : null;\n                const requestSubscription = client.request({\n                    op,\n                    transformer\n                }).subscribe(observer);\n                return ()=>{\n                    requestSubscription.unsubscribe();\n                    connStateSubscription?.unsubscribe();\n                };\n            });\n        };\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@trpc+client@11.1.2_@trpc+s_fc9643cca0c88fd471cb66420cf22d34/node_modules/@trpc/client/dist/links/wsLink/wsLink.mjs\n");

/***/ })

};
;