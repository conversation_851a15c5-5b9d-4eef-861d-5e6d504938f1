"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack+form-core@1.11.1";
exports.ids = ["vendor-chunks/@tanstack+form-core@1.11.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/FieldApi.js":
/*!*****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/FieldApi.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FieldApi: () => (/* binding */ FieldApi)\n/* harmony export */ });\n/* harmony import */ var _tanstack_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/store */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/scheduler.js\");\n/* harmony import */ var _tanstack_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/store */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/derived.js\");\n/* harmony import */ var _standardSchemaValidator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./standardSchemaValidator.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/standardSchemaValidator.js\");\n/* harmony import */ var _metaHelper_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./metaHelper.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/metaHelper.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/utils.js\");\n\n\n\n\nclass FieldApi {\n  /**\n   * Initializes a new `FieldApi` instance.\n   */\n  constructor(opts) {\n    this.options = {};\n    this.mount = () => {\n      var _a, _b;\n      const cleanup = this.store.mount();\n      if (this.options.defaultValue !== void 0) {\n        this.form.setFieldValue(this.name, this.options.defaultValue, {\n          dontUpdateMeta: true\n        });\n      }\n      const info = this.getInfo();\n      info.instance = this;\n      this.update(this.options);\n      const { onMount } = this.options.validators || {};\n      if (onMount) {\n        const error = this.runValidator({\n          validate: onMount,\n          value: {\n            value: this.state.value,\n            fieldApi: this,\n            validationSource: \"field\"\n          },\n          type: \"validate\"\n        });\n        if (error) {\n          this.setMeta(\n            (prev) => ({\n              ...prev,\n              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n              errorMap: { ...prev == null ? void 0 : prev.errorMap, onMount: error },\n              errorSourceMap: {\n                // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n                ...prev == null ? void 0 : prev.errorSourceMap,\n                onMount: \"field\"\n              }\n            })\n          );\n        }\n      }\n      (_b = (_a = this.options.listeners) == null ? void 0 : _a.onMount) == null ? void 0 : _b.call(_a, {\n        value: this.state.value,\n        fieldApi: this\n      });\n      return cleanup;\n    };\n    this.update = (opts2) => {\n      this.options = opts2;\n      const nameHasChanged = this.name !== opts2.name;\n      this.name = opts2.name;\n      if (this.state.value === void 0) {\n        const formDefault = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getBy)(opts2.form.options.defaultValues, opts2.name);\n        const defaultValue = opts2.defaultValue ?? formDefault;\n        if (nameHasChanged) {\n          this.setValue((val) => val || defaultValue, {\n            dontUpdateMeta: true\n          });\n        } else if (defaultValue !== void 0) {\n          this.setValue(defaultValue, {\n            dontUpdateMeta: true\n          });\n        }\n      }\n      if (this.form.getFieldMeta(this.name) === void 0) {\n        this.setMeta(this.state.meta);\n      }\n    };\n    this.getValue = () => {\n      return this.form.getFieldValue(this.name);\n    };\n    this.setValue = (updater, options) => {\n      this.form.setFieldValue(this.name, updater, options);\n      this.triggerOnChangeListener();\n      this.validate(\"change\");\n    };\n    this.getMeta = () => this.store.state.meta;\n    this.setMeta = (updater) => this.form.setFieldMeta(this.name, updater);\n    this.getInfo = () => this.form.getFieldInfo(this.name);\n    this.pushValue = (value, opts2) => {\n      this.form.pushFieldValue(this.name, value, opts2);\n      this.triggerOnChangeListener();\n    };\n    this.insertValue = (index, value, opts2) => {\n      this.form.insertFieldValue(this.name, index, value, opts2);\n      this.triggerOnChangeListener();\n    };\n    this.replaceValue = (index, value, opts2) => {\n      this.form.replaceFieldValue(this.name, index, value, opts2);\n      this.triggerOnChangeListener();\n    };\n    this.removeValue = (index, opts2) => {\n      this.form.removeFieldValue(this.name, index, opts2);\n      this.triggerOnChangeListener();\n    };\n    this.swapValues = (aIndex, bIndex, opts2) => {\n      this.form.swapFieldValues(this.name, aIndex, bIndex, opts2);\n      this.triggerOnChangeListener();\n    };\n    this.moveValue = (aIndex, bIndex, opts2) => {\n      this.form.moveFieldValues(this.name, aIndex, bIndex, opts2);\n      this.triggerOnChangeListener();\n    };\n    this.getLinkedFields = (cause) => {\n      const fields = Object.values(this.form.fieldInfo);\n      const linkedFields = [];\n      for (const field of fields) {\n        if (!field.instance) continue;\n        const { onChangeListenTo, onBlurListenTo } = field.instance.options.validators || {};\n        if (cause === \"change\" && (onChangeListenTo == null ? void 0 : onChangeListenTo.includes(this.name))) {\n          linkedFields.push(field.instance);\n        }\n        if (cause === \"blur\" && (onBlurListenTo == null ? void 0 : onBlurListenTo.includes(this.name))) {\n          linkedFields.push(field.instance);\n        }\n      }\n      return linkedFields;\n    };\n    this.validateSync = (cause, errorFromForm) => {\n      var _a;\n      const validates = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSyncValidatorArray)(cause, this.options);\n      const linkedFields = this.getLinkedFields(cause);\n      const linkedFieldValidates = linkedFields.reduce(\n        (acc, field) => {\n          const fieldValidates = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSyncValidatorArray)(cause, field.options);\n          fieldValidates.forEach((validate) => {\n            validate.field = field;\n          });\n          return acc.concat(fieldValidates);\n        },\n        []\n      );\n      let hasErrored = false;\n      (0,_tanstack_store__WEBPACK_IMPORTED_MODULE_1__.batch)(() => {\n        const validateFieldFn = (field, validateObj) => {\n          var _a2;\n          const errorMapKey = getErrorMapKey(validateObj.cause);\n          const fieldLevelError = validateObj.validate ? normalizeError(\n            field.runValidator({\n              validate: validateObj.validate,\n              value: {\n                value: field.store.state.value,\n                validationSource: \"field\",\n                fieldApi: field\n              },\n              type: \"validate\"\n            })\n          ) : void 0;\n          const formLevelError = errorFromForm[errorMapKey];\n          const { newErrorValue, newSource } = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.determineFieldLevelErrorSourceAndValue)({\n            formLevelError,\n            fieldLevelError\n          });\n          if (((_a2 = field.state.meta.errorMap) == null ? void 0 : _a2[errorMapKey]) !== newErrorValue) {\n            field.setMeta((prev) => ({\n              ...prev,\n              errorMap: {\n                ...prev.errorMap,\n                [errorMapKey]: newErrorValue\n              },\n              errorSourceMap: {\n                ...prev.errorSourceMap,\n                [errorMapKey]: newSource\n              }\n            }));\n          }\n          if (newErrorValue) {\n            hasErrored = true;\n          }\n        };\n        for (const validateObj of validates) {\n          validateFieldFn(this, validateObj);\n        }\n        for (const fieldValitateObj of linkedFieldValidates) {\n          if (!fieldValitateObj.validate) continue;\n          validateFieldFn(fieldValitateObj.field, fieldValitateObj);\n        }\n      });\n      const submitErrKey = getErrorMapKey(\"submit\");\n      if (\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        ((_a = this.state.meta.errorMap) == null ? void 0 : _a[submitErrKey]) && cause !== \"submit\" && !hasErrored\n      ) {\n        this.setMeta((prev) => ({\n          ...prev,\n          errorMap: {\n            ...prev.errorMap,\n            [submitErrKey]: void 0\n          },\n          errorSourceMap: {\n            ...prev.errorSourceMap,\n            [submitErrKey]: void 0\n          }\n        }));\n      }\n      return { hasErrored };\n    };\n    this.validateAsync = async (cause, formValidationResultPromise) => {\n      const validates = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getAsyncValidatorArray)(cause, this.options);\n      const asyncFormValidationResults = await formValidationResultPromise;\n      const linkedFields = this.getLinkedFields(cause);\n      const linkedFieldValidates = linkedFields.reduce(\n        (acc, field) => {\n          const fieldValidates = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getAsyncValidatorArray)(cause, field.options);\n          fieldValidates.forEach((validate) => {\n            validate.field = field;\n          });\n          return acc.concat(fieldValidates);\n        },\n        []\n      );\n      if (!this.state.meta.isValidating) {\n        this.setMeta((prev) => ({ ...prev, isValidating: true }));\n      }\n      for (const linkedField of linkedFields) {\n        linkedField.setMeta((prev) => ({ ...prev, isValidating: true }));\n      }\n      const validatesPromises = [];\n      const linkedPromises = [];\n      const validateFieldAsyncFn = (field, validateObj, promises) => {\n        const errorMapKey = getErrorMapKey(validateObj.cause);\n        const fieldValidatorMeta = field.getInfo().validationMetaMap[errorMapKey];\n        fieldValidatorMeta == null ? void 0 : fieldValidatorMeta.lastAbortController.abort();\n        const controller = new AbortController();\n        this.getInfo().validationMetaMap[errorMapKey] = {\n          lastAbortController: controller\n        };\n        promises.push(\n          new Promise(async (resolve) => {\n            var _a;\n            let rawError;\n            try {\n              rawError = await new Promise((rawResolve, rawReject) => {\n                if (this.timeoutIds.validations[validateObj.cause]) {\n                  clearTimeout(this.timeoutIds.validations[validateObj.cause]);\n                }\n                this.timeoutIds.validations[validateObj.cause] = setTimeout(\n                  async () => {\n                    if (controller.signal.aborted) return rawResolve(void 0);\n                    try {\n                      rawResolve(\n                        await this.runValidator({\n                          validate: validateObj.validate,\n                          value: {\n                            value: field.store.state.value,\n                            fieldApi: field,\n                            signal: controller.signal,\n                            validationSource: \"field\"\n                          },\n                          type: \"validateAsync\"\n                        })\n                      );\n                    } catch (e) {\n                      rawReject(e);\n                    }\n                  },\n                  validateObj.debounceMs\n                );\n              });\n            } catch (e) {\n              rawError = e;\n            }\n            if (controller.signal.aborted) return resolve(void 0);\n            const fieldLevelError = normalizeError(rawError);\n            const formLevelError = (_a = asyncFormValidationResults[this.name]) == null ? void 0 : _a[errorMapKey];\n            const { newErrorValue, newSource } = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.determineFieldLevelErrorSourceAndValue)({\n              formLevelError,\n              fieldLevelError\n            });\n            field.setMeta((prev) => {\n              return {\n                ...prev,\n                errorMap: {\n                  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n                  ...prev == null ? void 0 : prev.errorMap,\n                  [errorMapKey]: newErrorValue\n                },\n                errorSourceMap: {\n                  ...prev.errorSourceMap,\n                  [errorMapKey]: newSource\n                }\n              };\n            });\n            resolve(newErrorValue);\n          })\n        );\n      };\n      for (const validateObj of validates) {\n        if (!validateObj.validate) continue;\n        validateFieldAsyncFn(this, validateObj, validatesPromises);\n      }\n      for (const fieldValitateObj of linkedFieldValidates) {\n        if (!fieldValitateObj.validate) continue;\n        validateFieldAsyncFn(\n          fieldValitateObj.field,\n          fieldValitateObj,\n          linkedPromises\n        );\n      }\n      let results = [];\n      if (validatesPromises.length || linkedPromises.length) {\n        results = await Promise.all(validatesPromises);\n        await Promise.all(linkedPromises);\n      }\n      this.setMeta((prev) => ({ ...prev, isValidating: false }));\n      for (const linkedField of linkedFields) {\n        linkedField.setMeta((prev) => ({ ...prev, isValidating: false }));\n      }\n      return results.filter(Boolean);\n    };\n    this.validate = (cause, opts2) => {\n      var _a;\n      if (!this.state.meta.isTouched) return [];\n      const { fieldsErrorMap } = (opts2 == null ? void 0 : opts2.skipFormValidation) ? { fieldsErrorMap: {} } : this.form.validateSync(cause);\n      const { hasErrored } = this.validateSync(\n        cause,\n        fieldsErrorMap[this.name] ?? {}\n      );\n      if (hasErrored && !this.options.asyncAlways) {\n        (_a = this.getInfo().validationMetaMap[getErrorMapKey(cause)]) == null ? void 0 : _a.lastAbortController.abort();\n        return this.state.meta.errors;\n      }\n      const formValidationResultPromise = (opts2 == null ? void 0 : opts2.skipFormValidation) ? Promise.resolve({}) : this.form.validateAsync(cause);\n      return this.validateAsync(cause, formValidationResultPromise);\n    };\n    this.handleChange = (updater) => {\n      this.setValue(updater);\n    };\n    this.handleBlur = () => {\n      const prevTouched = this.state.meta.isTouched;\n      if (!prevTouched) {\n        this.setMeta((prev) => ({ ...prev, isTouched: true }));\n        this.validate(\"change\");\n      }\n      if (!this.state.meta.isBlurred) {\n        this.setMeta((prev) => ({ ...prev, isBlurred: true }));\n      }\n      this.validate(\"blur\");\n      this.triggerOnBlurListener();\n    };\n    this.parseValueWithSchema = (schema) => {\n      return _standardSchemaValidator_js__WEBPACK_IMPORTED_MODULE_2__.standardSchemaValidators.validate(\n        { value: this.state.value, validationSource: \"field\" },\n        schema\n      );\n    };\n    this.parseValueWithSchemaAsync = (schema) => {\n      return _standardSchemaValidator_js__WEBPACK_IMPORTED_MODULE_2__.standardSchemaValidators.validateAsync(\n        { value: this.state.value, validationSource: \"field\" },\n        schema\n      );\n    };\n    this.form = opts.form;\n    this.name = opts.name;\n    this.timeoutIds = {\n      validations: {},\n      listeners: {},\n      formListeners: {}\n    };\n    this.store = new _tanstack_store__WEBPACK_IMPORTED_MODULE_3__.Derived({\n      deps: [this.form.store],\n      fn: () => {\n        const value = this.form.getFieldValue(this.name);\n        const meta = this.form.getFieldMeta(this.name) ?? {\n          ..._metaHelper_js__WEBPACK_IMPORTED_MODULE_4__.defaultFieldMeta,\n          ...opts.defaultMeta\n        };\n        return {\n          value,\n          meta\n        };\n      }\n    });\n    this.options = opts;\n  }\n  /**\n   * The current field state.\n   */\n  get state() {\n    return this.store.state;\n  }\n  /**\n   * @private\n   */\n  runValidator(props) {\n    if ((0,_standardSchemaValidator_js__WEBPACK_IMPORTED_MODULE_2__.isStandardSchemaValidator)(props.validate)) {\n      return _standardSchemaValidator_js__WEBPACK_IMPORTED_MODULE_2__.standardSchemaValidators[props.type](\n        props.value,\n        props.validate\n      );\n    }\n    return props.validate(props.value);\n  }\n  /**\n   * Updates the field's errorMap\n   */\n  setErrorMap(errorMap) {\n    this.setMeta((prev) => ({\n      ...prev,\n      errorMap: {\n        ...prev.errorMap,\n        ...errorMap\n      }\n    }));\n  }\n  triggerOnBlurListener() {\n    var _a, _b, _c, _d, _e, _f;\n    const formDebounceMs = (_a = this.form.options.listeners) == null ? void 0 : _a.onBlurDebounceMs;\n    if (formDebounceMs && formDebounceMs > 0) {\n      if (this.timeoutIds.formListeners.blur) {\n        clearTimeout(this.timeoutIds.formListeners.blur);\n      }\n      this.timeoutIds.formListeners.blur = setTimeout(() => {\n        var _a2, _b2;\n        (_b2 = (_a2 = this.form.options.listeners) == null ? void 0 : _a2.onBlur) == null ? void 0 : _b2.call(_a2, {\n          formApi: this.form,\n          fieldApi: this\n        });\n      }, formDebounceMs);\n    } else {\n      (_c = (_b = this.form.options.listeners) == null ? void 0 : _b.onBlur) == null ? void 0 : _c.call(_b, {\n        formApi: this.form,\n        fieldApi: this\n      });\n    }\n    const fieldDebounceMs = (_d = this.options.listeners) == null ? void 0 : _d.onBlurDebounceMs;\n    if (fieldDebounceMs && fieldDebounceMs > 0) {\n      if (this.timeoutIds.listeners.blur) {\n        clearTimeout(this.timeoutIds.listeners.blur);\n      }\n      this.timeoutIds.listeners.blur = setTimeout(() => {\n        var _a2, _b2;\n        (_b2 = (_a2 = this.options.listeners) == null ? void 0 : _a2.onBlur) == null ? void 0 : _b2.call(_a2, {\n          value: this.state.value,\n          fieldApi: this\n        });\n      }, fieldDebounceMs);\n    } else {\n      (_f = (_e = this.options.listeners) == null ? void 0 : _e.onBlur) == null ? void 0 : _f.call(_e, {\n        value: this.state.value,\n        fieldApi: this\n      });\n    }\n  }\n  triggerOnChangeListener() {\n    var _a, _b, _c, _d, _e, _f;\n    const formDebounceMs = (_a = this.form.options.listeners) == null ? void 0 : _a.onChangeDebounceMs;\n    if (formDebounceMs && formDebounceMs > 0) {\n      if (this.timeoutIds.formListeners.blur) {\n        clearTimeout(this.timeoutIds.formListeners.blur);\n      }\n      this.timeoutIds.formListeners.blur = setTimeout(() => {\n        var _a2, _b2;\n        (_b2 = (_a2 = this.form.options.listeners) == null ? void 0 : _a2.onChange) == null ? void 0 : _b2.call(_a2, {\n          formApi: this.form,\n          fieldApi: this\n        });\n      }, formDebounceMs);\n    } else {\n      (_c = (_b = this.form.options.listeners) == null ? void 0 : _b.onChange) == null ? void 0 : _c.call(_b, {\n        formApi: this.form,\n        fieldApi: this\n      });\n    }\n    const fieldDebounceMs = (_d = this.options.listeners) == null ? void 0 : _d.onChangeDebounceMs;\n    if (fieldDebounceMs && fieldDebounceMs > 0) {\n      if (this.timeoutIds.listeners.change) {\n        clearTimeout(this.timeoutIds.listeners.change);\n      }\n      this.timeoutIds.listeners.change = setTimeout(() => {\n        var _a2, _b2;\n        (_b2 = (_a2 = this.options.listeners) == null ? void 0 : _a2.onChange) == null ? void 0 : _b2.call(_a2, {\n          value: this.state.value,\n          fieldApi: this\n        });\n      }, fieldDebounceMs);\n    } else {\n      (_f = (_e = this.options.listeners) == null ? void 0 : _e.onChange) == null ? void 0 : _f.call(_e, {\n        value: this.state.value,\n        fieldApi: this\n      });\n    }\n  }\n}\nfunction normalizeError(rawError) {\n  if (rawError) {\n    return rawError;\n  }\n  return void 0;\n}\nfunction getErrorMapKey(cause) {\n  switch (cause) {\n    case \"submit\":\n      return \"onSubmit\";\n    case \"blur\":\n      return \"onBlur\";\n    case \"mount\":\n      return \"onMount\";\n    case \"server\":\n      return \"onServer\";\n    case \"change\":\n    default:\n      return \"onChange\";\n  }\n}\n\n//# sourceMappingURL=FieldApi.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/FieldApi.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/FormApi.js":
/*!****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/FormApi.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FormApi: () => (/* binding */ FormApi)\n/* harmony export */ });\n/* harmony import */ var _tanstack_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/store */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/scheduler.js\");\n/* harmony import */ var _tanstack_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/store */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/store.js\");\n/* harmony import */ var _tanstack_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/store */ \"(ssr)/../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/dist/esm/derived.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/utils.js\");\n/* harmony import */ var _standardSchemaValidator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./standardSchemaValidator.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/standardSchemaValidator.js\");\n/* harmony import */ var _metaHelper_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./metaHelper.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/metaHelper.js\");\n\n\n\n\nfunction getDefaultFormState(defaultState) {\n  return {\n    values: defaultState.values ?? {},\n    errorMap: defaultState.errorMap ?? {},\n    fieldMetaBase: defaultState.fieldMetaBase ?? {},\n    isSubmitted: defaultState.isSubmitted ?? false,\n    isSubmitting: defaultState.isSubmitting ?? false,\n    isValidating: defaultState.isValidating ?? false,\n    submissionAttempts: defaultState.submissionAttempts ?? 0,\n    isSubmitSuccessful: defaultState.isSubmitSuccessful ?? false,\n    validationMetaMap: defaultState.validationMetaMap ?? {\n      onChange: void 0,\n      onBlur: void 0,\n      onSubmit: void 0,\n      onMount: void 0,\n      onServer: void 0\n    }\n  };\n}\nclass FormApi {\n  /**\n   * Constructs a new `FormApi` instance with the given form options.\n   */\n  constructor(opts) {\n    var _a;\n    this.options = {};\n    this.fieldInfo = {};\n    this.prevTransformArray = [];\n    this.mount = () => {\n      var _a2, _b;\n      const cleanupFieldMetaDerived = this.fieldMetaDerived.mount();\n      const cleanupStoreDerived = this.store.mount();\n      const cleanup = () => {\n        cleanupFieldMetaDerived();\n        cleanupStoreDerived();\n      };\n      (_b = (_a2 = this.options.listeners) == null ? void 0 : _a2.onMount) == null ? void 0 : _b.call(_a2, { formApi: this });\n      const { onMount } = this.options.validators || {};\n      if (!onMount) return cleanup;\n      this.validateSync(\"mount\");\n      return cleanup;\n    };\n    this.update = (options) => {\n      var _a2, _b;\n      if (!options) return;\n      const oldOptions = this.options;\n      this.options = options;\n      const shouldUpdateReeval = !!((_b = (_a2 = options.transform) == null ? void 0 : _a2.deps) == null ? void 0 : _b.some(\n        (val, i) => val !== this.prevTransformArray[i]\n      ));\n      const shouldUpdateValues = options.defaultValues && !(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options.defaultValues, oldOptions.defaultValues) && !this.state.isTouched;\n      const shouldUpdateState = !(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options.defaultState, oldOptions.defaultState) && !this.state.isTouched;\n      if (!shouldUpdateValues && !shouldUpdateState && !shouldUpdateReeval) return;\n      (0,_tanstack_store__WEBPACK_IMPORTED_MODULE_1__.batch)(() => {\n        this.baseStore.setState(\n          () => getDefaultFormState(\n            Object.assign(\n              {},\n              this.state,\n              shouldUpdateState ? options.defaultState : {},\n              shouldUpdateValues ? {\n                values: options.defaultValues\n              } : {},\n              shouldUpdateReeval ? { _force_re_eval: !this.state._force_re_eval } : {}\n            )\n          )\n        );\n      });\n    };\n    this.reset = (values, opts2) => {\n      const { fieldMeta: currentFieldMeta } = this.state;\n      const fieldMetaBase = this.resetFieldMeta(currentFieldMeta);\n      if (values && !(opts2 == null ? void 0 : opts2.keepDefaultValues)) {\n        this.options = {\n          ...this.options,\n          defaultValues: values\n        };\n      }\n      this.baseStore.setState(\n        () => {\n          var _a2;\n          return getDefaultFormState({\n            ...this.options.defaultState,\n            values: values ?? this.options.defaultValues ?? ((_a2 = this.options.defaultState) == null ? void 0 : _a2.values),\n            fieldMetaBase\n          });\n        }\n      );\n    };\n    this.validateAllFields = async (cause) => {\n      const fieldValidationPromises = [];\n      (0,_tanstack_store__WEBPACK_IMPORTED_MODULE_1__.batch)(() => {\n        void Object.values(this.fieldInfo).forEach(\n          (field) => {\n            if (!field.instance) return;\n            const fieldInstance = field.instance;\n            fieldValidationPromises.push(\n              // Remember, `validate` is either a sync operation or a promise\n              Promise.resolve().then(\n                () => fieldInstance.validate(cause, { skipFormValidation: true })\n              )\n            );\n            if (!field.instance.state.meta.isTouched) {\n              field.instance.setMeta((prev) => ({ ...prev, isTouched: true }));\n            }\n          }\n        );\n      });\n      const fieldErrorMapMap = await Promise.all(fieldValidationPromises);\n      return fieldErrorMapMap.flat();\n    };\n    this.validateArrayFieldsStartingFrom = async (field, index, cause) => {\n      const currentValue = this.getFieldValue(field);\n      const lastIndex = Array.isArray(currentValue) ? Math.max(currentValue.length - 1, 0) : null;\n      const fieldKeysToValidate = [`${field}[${index}]`];\n      for (let i = index + 1; i <= (lastIndex ?? 0); i++) {\n        fieldKeysToValidate.push(`${field}[${i}]`);\n      }\n      const fieldsToValidate = Object.keys(this.fieldInfo).filter(\n        (fieldKey) => fieldKeysToValidate.some((key) => fieldKey.startsWith(key))\n      );\n      const fieldValidationPromises = [];\n      (0,_tanstack_store__WEBPACK_IMPORTED_MODULE_1__.batch)(() => {\n        fieldsToValidate.forEach((nestedField) => {\n          fieldValidationPromises.push(\n            Promise.resolve().then(() => this.validateField(nestedField, cause))\n          );\n        });\n      });\n      const fieldErrorMapMap = await Promise.all(fieldValidationPromises);\n      return fieldErrorMapMap.flat();\n    };\n    this.validateField = (field, cause) => {\n      var _a2;\n      const fieldInstance = (_a2 = this.fieldInfo[field]) == null ? void 0 : _a2.instance;\n      if (!fieldInstance) return [];\n      if (!fieldInstance.state.meta.isTouched) {\n        fieldInstance.setMeta((prev) => ({ ...prev, isTouched: true }));\n      }\n      return fieldInstance.validate(cause);\n    };\n    this.validateSync = (cause) => {\n      const validates = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSyncValidatorArray)(cause, this.options);\n      let hasErrored = false;\n      const currentValidationErrorMap = {};\n      (0,_tanstack_store__WEBPACK_IMPORTED_MODULE_1__.batch)(() => {\n        var _a2, _b;\n        for (const validateObj of validates) {\n          if (!validateObj.validate) continue;\n          const rawError = this.runValidator({\n            validate: validateObj.validate,\n            value: {\n              value: this.state.values,\n              formApi: this,\n              validationSource: \"form\"\n            },\n            type: \"validate\"\n          });\n          const { formError, fieldErrors } = normalizeError(rawError);\n          const errorMapKey = getErrorMapKey(validateObj.cause);\n          for (const field of Object.keys(\n            this.state.fieldMeta\n          )) {\n            const fieldMeta = this.getFieldMeta(field);\n            if (!fieldMeta) continue;\n            const {\n              errorMap: currentErrorMap,\n              errorSourceMap: currentErrorMapSource\n            } = fieldMeta;\n            const newFormValidatorError = fieldErrors == null ? void 0 : fieldErrors[field];\n            const { newErrorValue, newSource } = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.determineFormLevelErrorSourceAndValue)({\n              newFormValidatorError,\n              isPreviousErrorFromFormValidator: (\n                // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n                (currentErrorMapSource == null ? void 0 : currentErrorMapSource[errorMapKey]) === \"form\"\n              ),\n              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n              previousErrorValue: currentErrorMap == null ? void 0 : currentErrorMap[errorMapKey]\n            });\n            if (newSource === \"form\") {\n              currentValidationErrorMap[field] = {\n                ...currentValidationErrorMap[field],\n                [errorMapKey]: newFormValidatorError\n              };\n            }\n            if (\n              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n              (currentErrorMap == null ? void 0 : currentErrorMap[errorMapKey]) !== newErrorValue\n            ) {\n              this.setFieldMeta(field, (prev) => ({\n                ...prev,\n                errorMap: {\n                  ...prev.errorMap,\n                  [errorMapKey]: newErrorValue\n                },\n                errorSourceMap: {\n                  ...prev.errorSourceMap,\n                  [errorMapKey]: newSource\n                }\n              }));\n            }\n          }\n          if (((_a2 = this.state.errorMap) == null ? void 0 : _a2[errorMapKey]) !== formError) {\n            this.baseStore.setState((prev) => ({\n              ...prev,\n              errorMap: {\n                ...prev.errorMap,\n                [errorMapKey]: formError\n              }\n            }));\n          }\n          if (formError || fieldErrors) {\n            hasErrored = true;\n          }\n        }\n        const submitErrKey = getErrorMapKey(\"submit\");\n        if (\n          // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n          ((_b = this.state.errorMap) == null ? void 0 : _b[submitErrKey]) && cause !== \"submit\" && !hasErrored\n        ) {\n          this.baseStore.setState((prev) => ({\n            ...prev,\n            errorMap: {\n              ...prev.errorMap,\n              [submitErrKey]: void 0\n            }\n          }));\n        }\n      });\n      return { hasErrored, fieldsErrorMap: currentValidationErrorMap };\n    };\n    this.validateAsync = async (cause) => {\n      const validates = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getAsyncValidatorArray)(cause, this.options);\n      if (!this.state.isFormValidating) {\n        this.baseStore.setState((prev) => ({ ...prev, isFormValidating: true }));\n      }\n      const promises = [];\n      let fieldErrorsFromFormValidators;\n      for (const validateObj of validates) {\n        if (!validateObj.validate) continue;\n        const key = getErrorMapKey(validateObj.cause);\n        const fieldValidatorMeta = this.state.validationMetaMap[key];\n        fieldValidatorMeta == null ? void 0 : fieldValidatorMeta.lastAbortController.abort();\n        const controller = new AbortController();\n        this.state.validationMetaMap[key] = {\n          lastAbortController: controller\n        };\n        promises.push(\n          new Promise(async (resolve) => {\n            let rawError;\n            try {\n              rawError = await new Promise((rawResolve, rawReject) => {\n                setTimeout(async () => {\n                  if (controller.signal.aborted) return rawResolve(void 0);\n                  try {\n                    rawResolve(\n                      await this.runValidator({\n                        validate: validateObj.validate,\n                        value: {\n                          value: this.state.values,\n                          formApi: this,\n                          validationSource: \"form\",\n                          signal: controller.signal\n                        },\n                        type: \"validateAsync\"\n                      })\n                    );\n                  } catch (e) {\n                    rawReject(e);\n                  }\n                }, validateObj.debounceMs);\n              });\n            } catch (e) {\n              rawError = e;\n            }\n            const { formError, fieldErrors: fieldErrorsFromNormalizeError } = normalizeError(rawError);\n            if (fieldErrorsFromNormalizeError) {\n              fieldErrorsFromFormValidators = fieldErrorsFromFormValidators ? {\n                ...fieldErrorsFromFormValidators,\n                ...fieldErrorsFromNormalizeError\n              } : fieldErrorsFromNormalizeError;\n            }\n            const errorMapKey = getErrorMapKey(validateObj.cause);\n            for (const field of Object.keys(\n              this.state.fieldMeta\n            )) {\n              const fieldMeta = this.getFieldMeta(field);\n              if (!fieldMeta) continue;\n              const {\n                errorMap: currentErrorMap,\n                errorSourceMap: currentErrorMapSource\n              } = fieldMeta;\n              const newFormValidatorError = fieldErrorsFromFormValidators == null ? void 0 : fieldErrorsFromFormValidators[field];\n              const { newErrorValue, newSource } = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.determineFormLevelErrorSourceAndValue)({\n                newFormValidatorError,\n                isPreviousErrorFromFormValidator: (\n                  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n                  (currentErrorMapSource == null ? void 0 : currentErrorMapSource[errorMapKey]) === \"form\"\n                ),\n                // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n                previousErrorValue: currentErrorMap == null ? void 0 : currentErrorMap[errorMapKey]\n              });\n              if (\n                // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n                (currentErrorMap == null ? void 0 : currentErrorMap[errorMapKey]) !== newErrorValue\n              ) {\n                this.setFieldMeta(field, (prev) => ({\n                  ...prev,\n                  errorMap: {\n                    ...prev.errorMap,\n                    [errorMapKey]: newErrorValue\n                  },\n                  errorSourceMap: {\n                    ...prev.errorSourceMap,\n                    [errorMapKey]: newSource\n                  }\n                }));\n              }\n            }\n            this.baseStore.setState((prev) => ({\n              ...prev,\n              errorMap: {\n                ...prev.errorMap,\n                [errorMapKey]: formError\n              }\n            }));\n            resolve(\n              fieldErrorsFromFormValidators ? { fieldErrors: fieldErrorsFromFormValidators, errorMapKey } : void 0\n            );\n          })\n        );\n      }\n      let results = [];\n      const fieldsErrorMap = {};\n      if (promises.length) {\n        results = await Promise.all(promises);\n        for (const fieldValidationResult of results) {\n          if (fieldValidationResult == null ? void 0 : fieldValidationResult.fieldErrors) {\n            const { errorMapKey } = fieldValidationResult;\n            for (const [field, fieldError] of Object.entries(\n              fieldValidationResult.fieldErrors\n            )) {\n              const oldErrorMap = fieldsErrorMap[field] || {};\n              const newErrorMap = {\n                ...oldErrorMap,\n                [errorMapKey]: fieldError\n              };\n              fieldsErrorMap[field] = newErrorMap;\n            }\n          }\n        }\n      }\n      this.baseStore.setState((prev) => ({\n        ...prev,\n        isFormValidating: false\n      }));\n      return fieldsErrorMap;\n    };\n    this.validate = (cause) => {\n      const { hasErrored, fieldsErrorMap } = this.validateSync(cause);\n      if (hasErrored && !this.options.asyncAlways) {\n        return fieldsErrorMap;\n      }\n      return this.validateAsync(cause);\n    };\n    this.getFieldValue = (field) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getBy)(this.state.values, field);\n    this.getFieldMeta = (field) => {\n      return this.state.fieldMeta[field];\n    };\n    this.getFieldInfo = (field) => {\n      var _a2;\n      return (_a2 = this.fieldInfo)[field] || (_a2[field] = {\n        instance: null,\n        validationMetaMap: {\n          onChange: void 0,\n          onBlur: void 0,\n          onSubmit: void 0,\n          onMount: void 0,\n          onServer: void 0\n        }\n      });\n    };\n    this.setFieldMeta = (field, updater) => {\n      this.baseStore.setState((prev) => {\n        return {\n          ...prev,\n          fieldMetaBase: {\n            ...prev.fieldMetaBase,\n            [field]: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.functionalUpdate)(\n              updater,\n              prev.fieldMetaBase[field]\n            )\n          }\n        };\n      });\n    };\n    this.resetFieldMeta = (fieldMeta) => {\n      return Object.keys(fieldMeta).reduce(\n        (acc, key) => {\n          const fieldKey = key;\n          acc[fieldKey] = _metaHelper_js__WEBPACK_IMPORTED_MODULE_2__.defaultFieldMeta;\n          return acc;\n        },\n        {}\n      );\n    };\n    this.setFieldValue = (field, updater, opts2) => {\n      const dontUpdateMeta = (opts2 == null ? void 0 : opts2.dontUpdateMeta) ?? false;\n      (0,_tanstack_store__WEBPACK_IMPORTED_MODULE_1__.batch)(() => {\n        if (!dontUpdateMeta) {\n          this.setFieldMeta(field, (prev) => ({\n            ...prev,\n            isTouched: true,\n            isDirty: true,\n            errorMap: {\n              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n              ...prev == null ? void 0 : prev.errorMap,\n              onMount: void 0\n            }\n          }));\n        }\n        this.baseStore.setState((prev) => {\n          return {\n            ...prev,\n            values: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.setBy)(prev.values, field, updater)\n          };\n        });\n      });\n    };\n    this.deleteField = (field) => {\n      const subFieldsToDelete = Object.keys(this.fieldInfo).filter((f) => {\n        const fieldStr = field.toString();\n        return f !== fieldStr && f.startsWith(fieldStr);\n      });\n      const fieldsToDelete = [...subFieldsToDelete, field];\n      this.baseStore.setState((prev) => {\n        const newState = { ...prev };\n        fieldsToDelete.forEach((f) => {\n          newState.values = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.deleteBy)(newState.values, f);\n          delete this.fieldInfo[f];\n          delete newState.fieldMetaBase[f];\n        });\n        return newState;\n      });\n    };\n    this.pushFieldValue = (field, value, opts2) => {\n      this.setFieldValue(\n        field,\n        (prev) => [...Array.isArray(prev) ? prev : [], value],\n        opts2\n      );\n      this.validateField(field, \"change\");\n    };\n    this.insertFieldValue = async (field, index, value, opts2) => {\n      this.setFieldValue(\n        field,\n        (prev) => {\n          return [\n            ...prev.slice(0, index),\n            value,\n            ...prev.slice(index)\n          ];\n        },\n        opts2\n      );\n      await this.validateField(field, \"change\");\n      (0,_metaHelper_js__WEBPACK_IMPORTED_MODULE_2__.metaHelper)(this).handleArrayFieldMetaShift(field, index, \"insert\");\n      await this.validateArrayFieldsStartingFrom(field, index, \"change\");\n    };\n    this.replaceFieldValue = async (field, index, value, opts2) => {\n      this.setFieldValue(\n        field,\n        (prev) => {\n          return prev.map(\n            (d, i) => i === index ? value : d\n          );\n        },\n        opts2\n      );\n      await this.validateField(field, \"change\");\n      await this.validateArrayFieldsStartingFrom(field, index, \"change\");\n    };\n    this.removeFieldValue = async (field, index, opts2) => {\n      const fieldValue = this.getFieldValue(field);\n      const lastIndex = Array.isArray(fieldValue) ? Math.max(fieldValue.length - 1, 0) : null;\n      this.setFieldValue(\n        field,\n        (prev) => {\n          return prev.filter(\n            (_d, i) => i !== index\n          );\n        },\n        opts2\n      );\n      (0,_metaHelper_js__WEBPACK_IMPORTED_MODULE_2__.metaHelper)(this).handleArrayFieldMetaShift(field, index, \"remove\");\n      if (lastIndex !== null) {\n        const start = `${field}[${lastIndex}]`;\n        this.deleteField(start);\n      }\n      await this.validateField(field, \"change\");\n      await this.validateArrayFieldsStartingFrom(field, index, \"change\");\n    };\n    this.swapFieldValues = (field, index1, index2, opts2) => {\n      this.setFieldValue(\n        field,\n        (prev) => {\n          const prev1 = prev[index1];\n          const prev2 = prev[index2];\n          return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.setBy)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.setBy)(prev, `${index1}`, prev2), `${index2}`, prev1);\n        },\n        opts2\n      );\n      (0,_metaHelper_js__WEBPACK_IMPORTED_MODULE_2__.metaHelper)(this).handleArrayFieldMetaShift(field, index1, \"swap\", index2);\n      this.validateField(field, \"change\");\n      this.validateField(`${field}[${index1}]`, \"change\");\n      this.validateField(`${field}[${index2}]`, \"change\");\n    };\n    this.moveFieldValues = (field, index1, index2, opts2) => {\n      this.setFieldValue(\n        field,\n        (prev) => {\n          prev.splice(index2, 0, prev.splice(index1, 1)[0]);\n          return prev;\n        },\n        opts2\n      );\n      (0,_metaHelper_js__WEBPACK_IMPORTED_MODULE_2__.metaHelper)(this).handleArrayFieldMetaShift(field, index1, \"move\", index2);\n      this.validateField(field, \"change\");\n      this.validateField(`${field}[${index1}]`, \"change\");\n      this.validateField(`${field}[${index2}]`, \"change\");\n    };\n    this.resetField = (field) => {\n      this.baseStore.setState((prev) => {\n        return {\n          ...prev,\n          fieldMetaBase: {\n            ...prev.fieldMetaBase,\n            [field]: _metaHelper_js__WEBPACK_IMPORTED_MODULE_2__.defaultFieldMeta\n          },\n          values: {\n            ...prev.values,\n            [field]: this.options.defaultValues && this.options.defaultValues[field]\n          }\n        };\n      });\n    };\n    this.getAllErrors = () => {\n      return {\n        form: {\n          errors: this.state.errors,\n          errorMap: this.state.errorMap\n        },\n        fields: Object.entries(this.state.fieldMeta).reduce(\n          (acc, [fieldName, fieldMeta]) => {\n            if (Object.keys(fieldMeta).length && fieldMeta.errors.length) {\n              acc[fieldName] = {\n                errors: fieldMeta.errors,\n                errorMap: fieldMeta.errorMap\n              };\n            }\n            return acc;\n          },\n          {}\n        )\n      };\n    };\n    this.parseValuesWithSchema = (schema) => {\n      return _standardSchemaValidator_js__WEBPACK_IMPORTED_MODULE_3__.standardSchemaValidators.validate(\n        { value: this.state.values, validationSource: \"form\" },\n        schema\n      );\n    };\n    this.parseValuesWithSchemaAsync = (schema) => {\n      return _standardSchemaValidator_js__WEBPACK_IMPORTED_MODULE_3__.standardSchemaValidators.validateAsync(\n        { value: this.state.values, validationSource: \"form\" },\n        schema\n      );\n    };\n    this.baseStore = new _tanstack_store__WEBPACK_IMPORTED_MODULE_4__.Store(\n      getDefaultFormState({\n        ...opts == null ? void 0 : opts.defaultState,\n        values: (opts == null ? void 0 : opts.defaultValues) ?? ((_a = opts == null ? void 0 : opts.defaultState) == null ? void 0 : _a.values)\n      })\n    );\n    this.fieldMetaDerived = new _tanstack_store__WEBPACK_IMPORTED_MODULE_5__.Derived({\n      deps: [this.baseStore],\n      fn: ({ prevDepVals, currDepVals, prevVal: _prevVal }) => {\n        var _a2, _b, _c;\n        const prevVal = _prevVal;\n        const prevBaseStore = prevDepVals == null ? void 0 : prevDepVals[0];\n        const currBaseStore = currDepVals[0];\n        let originalMetaCount = 0;\n        const fieldMeta = {};\n        for (const fieldName of Object.keys(\n          currBaseStore.fieldMetaBase\n        )) {\n          const currBaseMeta = currBaseStore.fieldMetaBase[fieldName];\n          const prevBaseMeta = prevBaseStore == null ? void 0 : prevBaseStore.fieldMetaBase[fieldName];\n          const prevFieldInfo = prevVal == null ? void 0 : prevVal[fieldName];\n          const curFieldVal = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getBy)(currBaseStore.values, fieldName);\n          let fieldErrors = prevFieldInfo == null ? void 0 : prevFieldInfo.errors;\n          if (!prevBaseMeta || currBaseMeta.errorMap !== prevBaseMeta.errorMap) {\n            fieldErrors = Object.values(currBaseMeta.errorMap ?? {}).filter(\n              (val) => val !== void 0\n            );\n            const fieldInstance = (_a2 = this.getFieldInfo(fieldName)) == null ? void 0 : _a2.instance;\n            if (fieldInstance && !fieldInstance.options.disableErrorFlat) {\n              fieldErrors = fieldErrors == null ? void 0 : fieldErrors.flat(\n                1\n              );\n            }\n          }\n          const isFieldValid = !(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isNonEmptyArray)(fieldErrors ?? []);\n          const isFieldPristine = !currBaseMeta.isDirty;\n          const isDefaultValue = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.evaluate)(\n            curFieldVal,\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getBy)(this.options.defaultValues, fieldName)\n          ) || (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.evaluate)(\n            curFieldVal,\n            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n            (_c = (_b = this.getFieldInfo(fieldName)) == null ? void 0 : _b.instance) == null ? void 0 : _c.options.defaultValue\n          );\n          if (prevFieldInfo && prevFieldInfo.isPristine === isFieldPristine && prevFieldInfo.isValid === isFieldValid && prevFieldInfo.isDefaultValue === isDefaultValue && prevFieldInfo.errors === fieldErrors && currBaseMeta === prevBaseMeta) {\n            fieldMeta[fieldName] = prevFieldInfo;\n            originalMetaCount++;\n            continue;\n          }\n          fieldMeta[fieldName] = {\n            ...currBaseMeta,\n            errors: fieldErrors,\n            isPristine: isFieldPristine,\n            isValid: isFieldValid,\n            isDefaultValue\n          };\n        }\n        if (!Object.keys(currBaseStore.fieldMetaBase).length) return fieldMeta;\n        if (prevVal && originalMetaCount === Object.keys(currBaseStore.fieldMetaBase).length) {\n          return prevVal;\n        }\n        return fieldMeta;\n      }\n    });\n    this.store = new _tanstack_store__WEBPACK_IMPORTED_MODULE_5__.Derived({\n      deps: [this.baseStore, this.fieldMetaDerived],\n      fn: ({ prevDepVals, currDepVals, prevVal: _prevVal }) => {\n        var _a2, _b, _c, _d;\n        const prevVal = _prevVal;\n        const prevBaseStore = prevDepVals == null ? void 0 : prevDepVals[0];\n        const currBaseStore = currDepVals[0];\n        const currFieldMeta = currDepVals[1];\n        const fieldMetaValues = Object.values(currFieldMeta).filter(\n          Boolean\n        );\n        const isFieldsValidating = fieldMetaValues.some(\n          (field) => field.isValidating\n        );\n        const isFieldsValid = fieldMetaValues.every((field) => field.isValid);\n        const isTouched = fieldMetaValues.some((field) => field.isTouched);\n        const isBlurred = fieldMetaValues.some((field) => field.isBlurred);\n        const isDefaultValue = fieldMetaValues.every(\n          (field) => field.isDefaultValue\n        );\n        const shouldInvalidateOnMount = (\n          // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n          isTouched && ((_a2 = currBaseStore.errorMap) == null ? void 0 : _a2.onMount)\n        );\n        const isDirty = fieldMetaValues.some((field) => field.isDirty);\n        const isPristine = !isDirty;\n        const hasOnMountError = Boolean(\n          // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n          ((_b = currBaseStore.errorMap) == null ? void 0 : _b.onMount) || // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n          fieldMetaValues.some((f) => {\n            var _a3;\n            return (_a3 = f == null ? void 0 : f.errorMap) == null ? void 0 : _a3.onMount;\n          })\n        );\n        const isValidating = !!isFieldsValidating;\n        let errors = (prevVal == null ? void 0 : prevVal.errors) ?? [];\n        if (!prevBaseStore || currBaseStore.errorMap !== prevBaseStore.errorMap) {\n          errors = Object.values(currBaseStore.errorMap).reduce((prev, curr) => {\n            if (curr === void 0) return prev;\n            if (curr && (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isGlobalFormValidationError)(curr)) {\n              prev.push(curr.form);\n              return prev;\n            }\n            prev.push(curr);\n            return prev;\n          }, []);\n        }\n        const isFormValid = errors.length === 0;\n        const isValid = isFieldsValid && isFormValid;\n        const submitInvalid = this.options.canSubmitWhenInvalid ?? false;\n        const canSubmit = currBaseStore.submissionAttempts === 0 && !isTouched && !hasOnMountError || !isValidating && !currBaseStore.isSubmitting && isValid || submitInvalid;\n        let errorMap = currBaseStore.errorMap;\n        if (shouldInvalidateOnMount) {\n          errors = errors.filter(\n            (err) => err !== currBaseStore.errorMap.onMount\n          );\n          errorMap = Object.assign(errorMap, { onMount: void 0 });\n        }\n        if (prevVal && prevBaseStore && prevVal.errorMap === errorMap && prevVal.fieldMeta === this.fieldMetaDerived.state && prevVal.errors === errors && prevVal.isFieldsValidating === isFieldsValidating && prevVal.isFieldsValid === isFieldsValid && prevVal.isFormValid === isFormValid && prevVal.isValid === isValid && prevVal.canSubmit === canSubmit && prevVal.isTouched === isTouched && prevVal.isBlurred === isBlurred && prevVal.isPristine === isPristine && prevVal.isDefaultValue === isDefaultValue && prevVal.isDirty === isDirty && (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.evaluate)(prevBaseStore, currBaseStore)) {\n          return prevVal;\n        }\n        let state = {\n          ...currBaseStore,\n          errorMap,\n          fieldMeta: this.fieldMetaDerived.state,\n          errors,\n          isFieldsValidating,\n          isFieldsValid,\n          isFormValid,\n          isValid,\n          canSubmit,\n          isTouched,\n          isBlurred,\n          isPristine,\n          isDefaultValue,\n          isDirty\n        };\n        const transformArray = ((_c = this.options.transform) == null ? void 0 : _c.deps) ?? [];\n        const shouldTransform = transformArray.length !== this.prevTransformArray.length || transformArray.some((val, i) => val !== this.prevTransformArray[i]);\n        if (shouldTransform) {\n          const newObj = Object.assign({}, this, { state });\n          (_d = this.options.transform) == null ? void 0 : _d.fn(newObj);\n          state = newObj.state;\n          this.prevTransformArray = transformArray;\n        }\n        return state;\n      }\n    });\n    this.handleSubmit = this.handleSubmit.bind(this);\n    this.update(opts || {});\n  }\n  get state() {\n    return this.store.state;\n  }\n  /**\n   * @private\n   */\n  runValidator(props) {\n    if ((0,_standardSchemaValidator_js__WEBPACK_IMPORTED_MODULE_3__.isStandardSchemaValidator)(props.validate)) {\n      return _standardSchemaValidator_js__WEBPACK_IMPORTED_MODULE_3__.standardSchemaValidators[props.type](\n        props.value,\n        props.validate\n      );\n    }\n    return props.validate(props.value);\n  }\n  async handleSubmit(submitMeta) {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    this.baseStore.setState((old) => ({\n      ...old,\n      // Submission attempts mark the form as not submitted\n      isSubmitted: false,\n      // Count submission attempts\n      submissionAttempts: old.submissionAttempts + 1,\n      isSubmitSuccessful: false\n      // Reset isSubmitSuccessful at the start of submission\n    }));\n    (0,_tanstack_store__WEBPACK_IMPORTED_MODULE_1__.batch)(() => {\n      void Object.values(this.fieldInfo).forEach(\n        (field) => {\n          if (!field.instance) return;\n          if (!field.instance.state.meta.isTouched) {\n            field.instance.setMeta((prev) => ({ ...prev, isTouched: true }));\n          }\n        }\n      );\n    });\n    if (!this.state.canSubmit) return;\n    this.baseStore.setState((d) => ({ ...d, isSubmitting: true }));\n    const done = () => {\n      this.baseStore.setState((prev) => ({ ...prev, isSubmitting: false }));\n    };\n    await this.validateAllFields(\"submit\");\n    if (!this.state.isFieldsValid) {\n      done();\n      (_b = (_a = this.options).onSubmitInvalid) == null ? void 0 : _b.call(_a, {\n        value: this.state.values,\n        formApi: this\n      });\n      return;\n    }\n    await this.validate(\"submit\");\n    if (!this.state.isValid) {\n      done();\n      (_d = (_c = this.options).onSubmitInvalid) == null ? void 0 : _d.call(_c, {\n        value: this.state.values,\n        formApi: this\n      });\n      return;\n    }\n    (0,_tanstack_store__WEBPACK_IMPORTED_MODULE_1__.batch)(() => {\n      void Object.values(this.fieldInfo).forEach(\n        (field) => {\n          var _a2, _b2, _c2;\n          (_c2 = (_b2 = (_a2 = field.instance) == null ? void 0 : _a2.options.listeners) == null ? void 0 : _b2.onSubmit) == null ? void 0 : _c2.call(_b2, {\n            value: field.instance.state.value,\n            fieldApi: field.instance\n          });\n        }\n      );\n    });\n    (_f = (_e = this.options.listeners) == null ? void 0 : _e.onSubmit) == null ? void 0 : _f.call(_e, { formApi: this });\n    try {\n      await ((_h = (_g = this.options).onSubmit) == null ? void 0 : _h.call(_g, {\n        value: this.state.values,\n        formApi: this,\n        meta: submitMeta ?? this.options.onSubmitMeta\n      }));\n      (0,_tanstack_store__WEBPACK_IMPORTED_MODULE_1__.batch)(() => {\n        this.baseStore.setState((prev) => ({\n          ...prev,\n          isSubmitted: true,\n          isSubmitSuccessful: true\n          // Set isSubmitSuccessful to true on successful submission\n        }));\n        done();\n      });\n    } catch (err) {\n      this.baseStore.setState((prev) => ({\n        ...prev,\n        isSubmitSuccessful: false\n        // Ensure isSubmitSuccessful is false if an error occurs\n      }));\n      done();\n      throw err;\n    }\n  }\n  /**\n   * Updates the form's errorMap\n   */\n  setErrorMap(errorMap) {\n    (0,_tanstack_store__WEBPACK_IMPORTED_MODULE_1__.batch)(() => {\n      Object.entries(errorMap).forEach(([key, value]) => {\n        const errorMapKey = key;\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isGlobalFormValidationError)(value)) {\n          const { formError, fieldErrors } = normalizeError(value);\n          for (const fieldName of Object.keys(\n            this.fieldInfo\n          )) {\n            const fieldMeta = this.getFieldMeta(fieldName);\n            if (!fieldMeta) continue;\n            this.setFieldMeta(fieldName, (prev) => ({\n              ...prev,\n              errorMap: {\n                ...prev.errorMap,\n                [errorMapKey]: fieldErrors == null ? void 0 : fieldErrors[fieldName]\n              },\n              errorSourceMap: {\n                ...prev.errorSourceMap,\n                [errorMapKey]: \"form\"\n              }\n            }));\n          }\n          this.baseStore.setState((prev) => ({\n            ...prev,\n            errorMap: {\n              ...prev.errorMap,\n              [errorMapKey]: formError\n            }\n          }));\n        } else {\n          this.baseStore.setState((prev) => ({\n            ...prev,\n            errorMap: {\n              ...prev.errorMap,\n              [errorMapKey]: value\n            }\n          }));\n        }\n      });\n    });\n  }\n}\nfunction normalizeError(rawError) {\n  if (rawError) {\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isGlobalFormValidationError)(rawError)) {\n      const formError = normalizeError(rawError.form).formError;\n      const fieldErrors = rawError.fields;\n      return { formError, fieldErrors };\n    }\n    return { formError: rawError };\n  }\n  return { formError: void 0 };\n}\nfunction getErrorMapKey(cause) {\n  switch (cause) {\n    case \"submit\":\n      return \"onSubmit\";\n    case \"blur\":\n      return \"onBlur\";\n    case \"mount\":\n      return \"onMount\";\n    case \"server\":\n      return \"onServer\";\n    case \"change\":\n    default:\n      return \"onChange\";\n  }\n}\n\n//# sourceMappingURL=FormApi.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/FormApi.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/metaHelper.js":
/*!*******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/metaHelper.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultFieldMeta: () => (/* binding */ defaultFieldMeta),\n/* harmony export */   metaHelper: () => (/* binding */ metaHelper)\n/* harmony export */ });\nconst defaultFieldMeta = {\n  isValidating: false,\n  isTouched: false,\n  isBlurred: false,\n  isDirty: false,\n  isPristine: true,\n  isValid: true,\n  isDefaultValue: true,\n  errors: [],\n  errorMap: {},\n  errorSourceMap: {}\n};\nfunction metaHelper(formApi) {\n  function handleArrayFieldMetaShift(field, index, mode, secondIndex) {\n    const affectedFields = getAffectedFields(field, index, mode, secondIndex);\n    const handlers = {\n      insert: () => handleInsertMode(affectedFields, field, index),\n      remove: () => handleRemoveMode(affectedFields),\n      swap: () => secondIndex !== void 0 && handleSwapMode(affectedFields, field, index, secondIndex),\n      move: () => secondIndex !== void 0 && handleMoveMode(affectedFields, field, index, secondIndex)\n    };\n    handlers[mode]();\n  }\n  function getFieldPath(field, index) {\n    return `${field}[${index}]`;\n  }\n  function getAffectedFields(field, index, mode, secondIndex) {\n    const affectedFieldKeys = [getFieldPath(field, index)];\n    if (mode === \"swap\") {\n      affectedFieldKeys.push(getFieldPath(field, secondIndex));\n    } else if (mode === \"move\") {\n      const [startIndex, endIndex] = [\n        Math.min(index, secondIndex),\n        Math.max(index, secondIndex)\n      ];\n      for (let i = startIndex; i <= endIndex; i++) {\n        affectedFieldKeys.push(getFieldPath(field, i));\n      }\n    } else {\n      const currentValue = formApi.getFieldValue(field);\n      const fieldItems = Array.isArray(currentValue) ? currentValue.length : 0;\n      for (let i = index + 1; i < fieldItems; i++) {\n        affectedFieldKeys.push(getFieldPath(field, i));\n      }\n    }\n    return Object.keys(formApi.fieldInfo).filter(\n      (fieldKey) => affectedFieldKeys.some((key) => fieldKey.startsWith(key))\n    );\n  }\n  function updateIndex(fieldKey, direction) {\n    return fieldKey.replace(/\\[(\\d+)\\]/, (_, num) => {\n      const currIndex = parseInt(num, 10);\n      const newIndex = direction === \"up\" ? currIndex + 1 : Math.max(0, currIndex - 1);\n      return `[${newIndex}]`;\n    });\n  }\n  function shiftMeta(fields, direction) {\n    const sortedFields = direction === \"up\" ? fields : [...fields].reverse();\n    sortedFields.forEach((fieldKey) => {\n      const nextFieldKey = updateIndex(fieldKey.toString(), direction);\n      const nextFieldMeta = formApi.getFieldMeta(nextFieldKey);\n      if (nextFieldMeta) {\n        formApi.setFieldMeta(fieldKey, nextFieldMeta);\n      } else {\n        formApi.setFieldMeta(fieldKey, getEmptyFieldMeta());\n      }\n    });\n  }\n  const getEmptyFieldMeta = () => defaultFieldMeta;\n  const handleInsertMode = (fields, field, insertIndex) => {\n    shiftMeta(fields, \"down\");\n    fields.forEach((fieldKey) => {\n      if (fieldKey.toString().startsWith(getFieldPath(field, insertIndex))) {\n        formApi.setFieldMeta(fieldKey, getEmptyFieldMeta());\n      }\n    });\n  };\n  const handleRemoveMode = (fields) => {\n    shiftMeta(fields, \"up\");\n  };\n  const handleMoveMode = (fields, field, fromIndex, toIndex) => {\n    const fromFields = new Map(\n      Object.keys(formApi.fieldInfo).filter(\n        (fieldKey) => fieldKey.startsWith(getFieldPath(field, fromIndex))\n      ).map((fieldKey) => [\n        fieldKey,\n        formApi.getFieldMeta(fieldKey)\n      ])\n    );\n    shiftMeta(fields, fromIndex < toIndex ? \"up\" : \"down\");\n    Object.keys(formApi.fieldInfo).filter((fieldKey) => fieldKey.startsWith(getFieldPath(field, toIndex))).forEach((fieldKey) => {\n      const fromKey = fieldKey.replace(\n        getFieldPath(field, toIndex),\n        getFieldPath(field, fromIndex)\n      );\n      const fromMeta = fromFields.get(fromKey);\n      if (fromMeta) {\n        formApi.setFieldMeta(fieldKey, fromMeta);\n      }\n    });\n  };\n  const handleSwapMode = (fields, field, index, secondIndex) => {\n    fields.forEach((fieldKey) => {\n      if (!fieldKey.toString().startsWith(getFieldPath(field, index))) return;\n      const swappedKey = fieldKey.toString().replace(\n        getFieldPath(field, index),\n        getFieldPath(field, secondIndex)\n      );\n      const [meta1, meta2] = [\n        formApi.getFieldMeta(fieldKey),\n        formApi.getFieldMeta(swappedKey)\n      ];\n      if (meta1) formApi.setFieldMeta(swappedKey, meta1);\n      if (meta2) formApi.setFieldMeta(fieldKey, meta2);\n    });\n  };\n  return { handleArrayFieldMetaShift };\n}\n\n//# sourceMappingURL=metaHelper.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/metaHelper.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/standardSchemaValidator.js":
/*!********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/standardSchemaValidator.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isStandardSchemaValidator: () => (/* binding */ isStandardSchemaValidator),\n/* harmony export */   standardSchemaValidators: () => (/* binding */ standardSchemaValidators)\n/* harmony export */ });\nfunction prefixSchemaToErrors(issues) {\n  const schema = /* @__PURE__ */ new Map();\n  for (const issue of issues) {\n    const path = [...issue.path ?? []].map((segment) => {\n      const normalizedSegment = typeof segment === \"object\" ? segment.key : segment;\n      return typeof normalizedSegment === \"number\" ? `[${normalizedSegment}]` : normalizedSegment;\n    }).join(\".\").replace(/\\.\\[/g, \"[\");\n    schema.set(path, (schema.get(path) ?? []).concat(issue));\n  }\n  return Object.fromEntries(schema);\n}\nconst transformFormIssues = (issues) => {\n  const schemaErrors = prefixSchemaToErrors(issues);\n  return {\n    form: schemaErrors,\n    fields: schemaErrors\n  };\n};\nconst standardSchemaValidators = {\n  validate({\n    value,\n    validationSource\n  }, schema) {\n    const result = schema[\"~standard\"].validate(value);\n    if (result instanceof Promise) {\n      throw new Error(\"async function passed to sync validator\");\n    }\n    if (!result.issues) return;\n    if (validationSource === \"field\")\n      return result.issues;\n    return transformFormIssues(result.issues);\n  },\n  async validateAsync({\n    value,\n    validationSource\n  }, schema) {\n    const result = await schema[\"~standard\"].validate(value);\n    if (!result.issues) return;\n    if (validationSource === \"field\")\n      return result.issues;\n    return transformFormIssues(result.issues);\n  }\n};\nconst isStandardSchemaValidator = (validator) => !!validator && \"~standard\" in validator;\n\n//# sourceMappingURL=standardSchemaValidator.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/standardSchemaValidator.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/utils.js":
/*!**************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/utils.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteBy: () => (/* binding */ deleteBy),\n/* harmony export */   determineFieldLevelErrorSourceAndValue: () => (/* binding */ determineFieldLevelErrorSourceAndValue),\n/* harmony export */   determineFormLevelErrorSourceAndValue: () => (/* binding */ determineFormLevelErrorSourceAndValue),\n/* harmony export */   evaluate: () => (/* binding */ evaluate),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   getAsyncValidatorArray: () => (/* binding */ getAsyncValidatorArray),\n/* harmony export */   getBy: () => (/* binding */ getBy),\n/* harmony export */   getSyncValidatorArray: () => (/* binding */ getSyncValidatorArray),\n/* harmony export */   isGlobalFormValidationError: () => (/* binding */ isGlobalFormValidationError),\n/* harmony export */   isNonEmptyArray: () => (/* binding */ isNonEmptyArray),\n/* harmony export */   makePathArray: () => (/* binding */ makePathArray),\n/* harmony export */   setBy: () => (/* binding */ setBy)\n/* harmony export */ });\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction getBy(obj, path) {\n  const pathObj = makePathArray(path);\n  return pathObj.reduce((current, pathPart) => {\n    if (current === null) return null;\n    if (typeof current !== \"undefined\") {\n      return current[pathPart];\n    }\n    return void 0;\n  }, obj);\n}\nfunction setBy(obj, _path, updater) {\n  const path = makePathArray(_path);\n  function doSet(parent) {\n    if (!path.length) {\n      return functionalUpdate(updater, parent);\n    }\n    const key = path.shift();\n    if (typeof key === \"string\" || typeof key === \"number\" && !Array.isArray(parent)) {\n      if (typeof parent === \"object\") {\n        if (parent === null) {\n          parent = {};\n        }\n        return {\n          ...parent,\n          [key]: doSet(parent[key])\n        };\n      }\n      return {\n        [key]: doSet()\n      };\n    }\n    if (Array.isArray(parent) && typeof key === \"number\") {\n      const prefix = parent.slice(0, key);\n      return [\n        ...prefix.length ? prefix : new Array(key),\n        doSet(parent[key]),\n        ...parent.slice(key + 1)\n      ];\n    }\n    return [...new Array(key), doSet()];\n  }\n  return doSet(obj);\n}\nfunction deleteBy(obj, _path) {\n  const path = makePathArray(_path);\n  function doDelete(parent) {\n    if (!parent) return;\n    if (path.length === 1) {\n      const finalPath = path[0];\n      if (Array.isArray(parent) && typeof finalPath === \"number\") {\n        return parent.filter((_, i) => i !== finalPath);\n      }\n      const { [finalPath]: remove, ...rest } = parent;\n      return rest;\n    }\n    const key = path.shift();\n    if (typeof key === \"string\") {\n      if (typeof parent === \"object\") {\n        return {\n          ...parent,\n          [key]: doDelete(parent[key])\n        };\n      }\n    }\n    if (typeof key === \"number\") {\n      if (Array.isArray(parent)) {\n        if (key >= parent.length) {\n          return parent;\n        }\n        const prefix = parent.slice(0, key);\n        return [\n          ...prefix.length ? prefix : new Array(key),\n          doDelete(parent[key]),\n          ...parent.slice(key + 1)\n        ];\n      }\n    }\n    throw new Error(\"It seems we have created an infinite loop in deleteBy. \");\n  }\n  return doDelete(obj);\n}\nconst reFindNumbers0 = /^(\\d*)$/gm;\nconst reFindNumbers1 = /\\.(\\d*)\\./gm;\nconst reFindNumbers2 = /^(\\d*)\\./gm;\nconst reFindNumbers3 = /\\.(\\d*$)/gm;\nconst reFindMultiplePeriods = /\\.{2,}/gm;\nconst intPrefix = \"__int__\";\nconst intReplace = `${intPrefix}$1`;\nfunction makePathArray(str) {\n  if (Array.isArray(str)) {\n    return [...str];\n  }\n  if (typeof str !== \"string\") {\n    throw new Error(\"Path must be a string.\");\n  }\n  return str.replace(/\\[/g, \".\").replace(/\\]/g, \"\").replace(reFindNumbers0, intReplace).replace(reFindNumbers1, `.${intReplace}.`).replace(reFindNumbers2, `${intReplace}.`).replace(reFindNumbers3, `.${intReplace}`).replace(reFindMultiplePeriods, \".\").split(\".\").map((d) => {\n    if (d.indexOf(intPrefix) === 0) {\n      return parseInt(d.substring(intPrefix.length), 10);\n    }\n    return d;\n  });\n}\nfunction isNonEmptyArray(obj) {\n  return !(Array.isArray(obj) && obj.length === 0);\n}\nfunction getAsyncValidatorArray(cause, options) {\n  const { asyncDebounceMs } = options;\n  const {\n    onChangeAsync,\n    onBlurAsync,\n    onSubmitAsync,\n    onBlurAsyncDebounceMs,\n    onChangeAsyncDebounceMs\n  } = options.validators || {};\n  const defaultDebounceMs = asyncDebounceMs ?? 0;\n  const changeValidator = {\n    cause: \"change\",\n    validate: onChangeAsync,\n    debounceMs: onChangeAsyncDebounceMs ?? defaultDebounceMs\n  };\n  const blurValidator = {\n    cause: \"blur\",\n    validate: onBlurAsync,\n    debounceMs: onBlurAsyncDebounceMs ?? defaultDebounceMs\n  };\n  const submitValidator = {\n    cause: \"submit\",\n    validate: onSubmitAsync,\n    debounceMs: 0\n  };\n  const noopValidator = (validator) => ({ ...validator, debounceMs: 0 });\n  switch (cause) {\n    case \"submit\":\n      return [\n        noopValidator(changeValidator),\n        noopValidator(blurValidator),\n        submitValidator\n      ];\n    case \"blur\":\n      return [blurValidator];\n    case \"change\":\n      return [changeValidator];\n    case \"server\":\n    default:\n      return [];\n  }\n}\nfunction getSyncValidatorArray(cause, options) {\n  const { onChange, onBlur, onSubmit, onMount } = options.validators || {};\n  const changeValidator = { cause: \"change\", validate: onChange };\n  const blurValidator = { cause: \"blur\", validate: onBlur };\n  const submitValidator = { cause: \"submit\", validate: onSubmit };\n  const mountValidator = { cause: \"mount\", validate: onMount };\n  const serverValidator = {\n    cause: \"server\",\n    validate: () => void 0\n  };\n  switch (cause) {\n    case \"mount\":\n      return [mountValidator];\n    case \"submit\":\n      return [\n        changeValidator,\n        blurValidator,\n        submitValidator,\n        serverValidator\n      ];\n    case \"server\":\n      return [serverValidator];\n    case \"blur\":\n      return [blurValidator, serverValidator];\n    case \"change\":\n    default:\n      return [changeValidator, serverValidator];\n  }\n}\nconst isGlobalFormValidationError = (error) => {\n  return !!error && typeof error === \"object\" && \"fields\" in error;\n};\nfunction evaluate(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false;\n    for (const [k, v] of objA) {\n      if (!objB.has(k) || !Object.is(v, objB.get(k))) return false;\n    }\n    return true;\n  }\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false;\n    for (const v of objA) {\n      if (!objB.has(v)) return false;\n    }\n    return true;\n  }\n  const keysA = Object.keys(objA);\n  const keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n  for (const key of keysA) {\n    if (!keysB.includes(key) || !evaluate(objA[key], objB[key])) {\n      return false;\n    }\n  }\n  return true;\n}\nconst determineFormLevelErrorSourceAndValue = ({\n  newFormValidatorError,\n  isPreviousErrorFromFormValidator,\n  previousErrorValue\n}) => {\n  if (newFormValidatorError) {\n    return { newErrorValue: newFormValidatorError, newSource: \"form\" };\n  }\n  if (isPreviousErrorFromFormValidator) {\n    return { newErrorValue: void 0, newSource: void 0 };\n  }\n  if (previousErrorValue) {\n    return { newErrorValue: previousErrorValue, newSource: \"field\" };\n  }\n  return { newErrorValue: void 0, newSource: void 0 };\n};\nconst determineFieldLevelErrorSourceAndValue = ({\n  formLevelError,\n  fieldLevelError\n}) => {\n  if (fieldLevelError) {\n    return { newErrorValue: fieldLevelError, newSource: \"field\" };\n  }\n  if (formLevelError) {\n    return { newErrorValue: formLevelError, newSource: \"form\" };\n  }\n  return { newErrorValue: void 0, newSource: void 0 };\n};\n\n//# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+form-core@1.11.1/node_modules/@tanstack/form-core/dist/esm/utils.js\n");

/***/ })

};
;